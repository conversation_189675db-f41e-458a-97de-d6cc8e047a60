<?php
namespace app;

use app\CustomException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use Throwable;

use think\exception\RouteNotFoundException;
use think\facade\Log;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
        CustomException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param  Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request   $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 添加自定义异常处理机制
        // 路由匹配错误
        if ($e instanceof RouteNotFoundException) {
            return throwResponse('', 10002, $e->getMessage()); //'当前访问路由未定义或不匹配'
        }

        // 参数验证错误
        if ($e instanceof ValidateException) {
            return throwResponse('', 10001, $e->getMessage());
        }

        // 自定义异常
        if ($e instanceof CustomException) {
            $level = $e->getLevel();
            if ($level == 'error') {
                Log::write($e->getError(), $level);
            }
            return throwResponse('', $e->getErrCode(), $e->getError());
        }

        if(env('APP_DEBUG') == false){

            if ($e instanceof \Exception) {
                return throwResponse([],-1,$e->getMessage());
            }

        }


        // 其他错误交给系统处理
        return parent::render($request, $e);
    }
}
