{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.2.5", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "elasticsearch/elasticsearch": "7.14", "guzzlehttp/guzzle": "7.4", "firebase/php-jwt": "5.2", "aliyuncs/oss-sdk-php": "^2.6", "phpoffice/phpexcel": "^1.8", "phpoffice/phpspreadsheet": "1.15.0", "league/html-to-markdown": "^5.1"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist"}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}