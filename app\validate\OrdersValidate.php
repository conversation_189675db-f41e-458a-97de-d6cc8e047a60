<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class OrdersValidate
 * @package app\validate
 */
class OrdersValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|id' => 'require|number',  //id
        'time|time' => 'date',  //time
        'uid|uid' => 'max:255',  //uid
        'admin_id|admin_id' => 'number',  //admin_id

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'time', 'uid', 'admin_id',],
        'edit'   => [ 'id', 'time', 'uid', 'admin_id',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'time', 'uid', 'admin_id', ])->remove('id', 'require')
            ;
    }




}