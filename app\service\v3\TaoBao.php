<?php

namespace app\service\v3;

use app\BaseService;
use DateTime;
use Exception;

/**
 * 淘宝订单获取器
 *
 * 负责从淘宝开放平台API获取订单数据，支持多店铺配置
 * 包含订单状态映射、重试机制、数据转换等功能
 *
 * <AUTHOR> Assistant
 * @version 2.0
 * @since 2024
 */
class TaoBao extends BaseService
{
    /** @var string 淘宝开放平台API基础URL */
    const API_URL = "https://eco.taobao.com/router/rest";

    /** @var string 淘宝API版本号 */
    const API_VERSION = "2.0";

    /**
     * 淘宝应用凭证配置
     * 存储不同应用的app_key和app_secret
     */
    const APPLICATIONS = [
        'app1' => [
            'app_key'    => '32323720',
            'app_secret' => '450dd2a5a9a7d92b15a2c2c40d92e079'
        ]
    ];


    /**
     * 店铺与应用的映射关系
     * 定义每个店铺使用哪个应用配置进行API调用
     */
    const SHOP_CONFIGURATIONS = [
        [
            "platform_type" => "淘系",
            "platform_name" => "佰酿美酒天猫",
            "shop_id"       => "227734657",
            'app'           => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type" => "淘系",
            "platform_name" => "佰酿科技（天猫旗舰店）",
            "shop_id"       => "419938814",
            'app'           => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type" => "淘系",
            "platform_name" => "桃公子淘宝店",
            "shop_id"       => "566768114",
            'app'           => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type" => "淘系",
            "platform_name" => "天猫（法国南部葡萄酒官方旗舰店）",
            "shop_id"       => "558695549",
            'app'           => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type" => "淘系",
            "platform_name" => "酒遇喵掌柜",
            "shop_id"       => "452890329",
            'app'           => self::APPLICATIONS['app1'],
        ],
        [
            "platform_type" => "淘系",
            "platform_name" => "美尼多天猫旗舰店",
            "shop_id"       => "541276454",
            'app'           => self::APPLICATIONS['app1'],
        ],
    ];


    /**
     * 订单状态映射表
     * 将淘宝订单状态映射为系统内部状态
     */
    const ORDER_STATUS_MAPPING = [
        "WAIT_BUYER_PAY"           => "PENDING",             // 等待买家付款
        "TRADE_NO_CREATE_PAY"      => "PENDING",         // 没有创建支付宝交易
        "WAIT_SELLER_SEND_GOODS"   => "PROCESSING",   // 等待卖家发货,即:买家已付款
        "SELLER_CONSIGNED_PART"    => "SHIPPED",       // 卖家部分发货
        "WAIT_BUYER_CONFIRM_GOODS" => "SHIPPED",    // 等待买家确认收货,即:卖家已发货
        "TRADE_BUYER_SIGNED"       => "COMPLETED",        // 买家已签收,货到付款专用
        "TRADE_FINISHED"           => "COMPLETED",            // 交易成功
        "TRADE_CLOSED"             => "CLOSED",                 // 付款以后用户退款成功，交易自动关闭
        "TRADE_CLOSED_BY_TAOBAO"   => "CANCELLED",    // 付款以前，卖家或买家主动关闭交易
        "PAY_PENDING"              => "PENDING",                 // 国际信用卡支付付款确认中
        "PAID_FORBID_CONSIGN"      => "PROCESSING"       // 已付款但禁止发货
    ];


    // 缓存属性
    protected array $accessTokenCache = [];
    protected array $shopInfoCache    = [];


    /**
     * 构造函数
     */
    public function __construct()
    {
    }

    /**
     * 根据店铺名称和支付方式动态选择支付渠道
     * 与Python版本逻辑保持一致
     *
     * @param string $shopName 店铺名称
     * @param string $paymentMethod 支付方式
     * @return string 支付渠道
     */
    private function getPaymentChannel(string $shopName, string $paymentMethod): string
    {
        // 判断是支付宝还是微信支付
        $isAlipay = ($paymentMethod === "支付宝支付" || $paymentMethod === "alipay");
        $isWechat = ($paymentMethod === "微信支付" || $paymentMethod === "wx");

        // 根据店铺名称和支付方式设置支付渠道（与Python版本完全一致）
        if (strpos($shopName, "佰酿美酒天猫") !== false) {
            if ($isAlipay) {
                return "科技支付宝-bainiangmeiiutianmao";
            } elseif ($isWechat) {
                return "佰酿美酒天猫平台";
            }
        } elseif (strpos($shopName, "佰酿科技（天猫旗舰店）") !== false) {
            if ($isAlipay) {
                return "科技支付宝<EMAIL>";
            } elseif ($isWechat) {
                return "天猫旗舰店平台";
            }
        } elseif (strpos($shopName, "桃公子淘宝店") !== false) {
            if ($isAlipay) {
                return "科技支付宝-taogongzitaojiuguan";
            } elseif ($isWechat) {
                return "桃公子淘宝店平台";
            }
        } elseif (strpos($shopName, "天猫（法国南部葡萄酒官方旗舰店）") !== false) {
            if ($isAlipay) {
                return "法南天猫支付宝";
            } elseif ($isWechat) {
                return "法南天猫平台余额";
            }
        } elseif (strpos($shopName, "酒遇喵掌柜") !== false) {
            if ($isAlipay) {
                return "科技支付宝-jiuyumiaozhanggui";
            } elseif ($isWechat) {
                return "酒遇喵掌柜平台余额";
            }
        } elseif (strpos($shopName, "美尼多") !== false) {
            if ($isAlipay) {
                return "科技支付卡-shyanjiusuo";
            } elseif ($isWechat) {
                return "美尼多天猫旗舰店平台";
            }
        }

        // 默认支付渠道
        return $paymentMethod;
    }

    private function getAccessToken(string $shopId): string
    {
        if (empty($this->accessTokenCache[$shopId])) {
            $shopInfo                        = \Curl::getXiaoHongShuShopInfo(['shop_id' => $shopId]);
            $this->accessTokenCache[$shopId] = $shopInfo['access_token'];
            $this->shopInfoCache[$shopId]    = $shopInfo;
        }

        return $this->accessTokenCache[$shopId];
    }

    /**
     * 获取指定店铺的订单数据
     *
     * @param array $shop 店铺信息
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array 订单列表
     */
    private function getShopOrders(array $shop, string $startTime, string $endTime): array
    {
        echo "获取{$shop['platform_name']}订单 {$startTime} - {$endTime}" . PHP_EOL;

        #region  获取所有订单ID
        $allOrderNumbers = [];
        $pageNo          = 1;
        $pageSize        = 40;  // 每页订单数量
        $hasNext         = true;
        while ($hasNext) {
            // 使用重试机制调用API，并减少获取的字段数量
            $result = $this->callTaobaoApi(
                $shop,
                "taobao.trades.sold.get",
                [
                    "start_created" => $startTime,
                    "end_created"   => $endTime,
                    "status"        => "ALL",  // 获取所有状态的订单
                    "page_no"       => $pageNo,
                    "page_size"     => $pageSize,
                    // 只获取订单基本信息，详细信息通过getOrderDetail单独获取
                    "fields"        => "tid,status,created"
                ]
            );

            // 解析API返回结果
            if (isset($result["trades_sold_get_response"])) {
                $response = $result["trades_sold_get_response"];

                // 获取订单列表
                if (isset($response["trades"]) && isset($response["trades"]["trade"])) {
                    $tradeList = $response["trades"]["trade"];
                    foreach ($tradeList as $tradeData) {
                        $allOrderNumbers[] = strval($tradeData["tid"] ?? "");
                    }
                }

                // 检查是否有下一页 - 使用总数和页码计算
                $totalResults = intval($response["total_results"] ?? 0);
                echo "第 {$pageNo} 页获取到 " . count($allOrderNumbers) . " 个订单ID，总订单数: {$totalResults}\n";

                if ($totalResults > $pageNo * $pageSize) {
                    $pageNo++;
                    $hasNext = true;
                } else {
                    $hasNext = false;
                }
            } else {
                echo "淘宝API返回数据格式错误: " . print_r($result, true) . "\n";
                break;
            }
        }

        // 获取订单详情
        $orders = [];
        foreach ($allOrderNumbers as $orderNumber) {
            $order = $this->fetchOrderDetail($shop, $orderNumber);
            if ($order) {
                $orders[] = $order;
            }
        }
        return $orders;
    }

    /**
     * 获取订单详情
     *
     * @param array $shop 店铺信息
     * @param string $orderNumber 订单号
     * @return array|null 订单详情数据
     */
    private function fetchOrderDetail(array $shop, string $orderId): ?array
    {
        $response = $this->callTaobaoApi(
            $shop,
            "taobao.trade.fullinfo.get",
            [
                "tid"    => $orderId,
                "fields" => "tid,payment_method,type,status,payment,buyer_message,seller_memo,created,pay_time,consign_time,end_time,receiver_name,receiver_address,receiver_mobile,receiver_phone,orders,promotion_details,received_payment"
            ]
        );
        return $this->transformOrderData($shop, $response);
    }

    /**
     * 将原始订单数据转换为标准化的订单对象
     *
     * @param array $shop 店铺信息
     * @param array $orderData 原始订单数据
     * @return array 标准化的订单数据
     */
    private function transformOrderData(array $shop, array $orderData): array
    {
        $orders = [];
        // 提取子订单信息
        $subOrders = $this->extractSubOrders($orderData['trade_fullinfo_get_response']['trade']);


        // 处理每个子订单
        foreach ($subOrders as $subOrder) {
            // 转换为Order对象
            $order = $this->convertToOrderObject($shop, $subOrder);
            if ($order) {
                $orders[] = $order;
            }
        }
        return $orders;
    }


    /**
     * 将淘宝API返回的交易数据转换为Order对象
     *
     * @param \stdClass $shop 店铺信息对象
     * @param array $tradeData 交易数据数组
     * @return Order|null 订单对象，转换失败时返回null
     * @throws \InvalidArgumentException 当参数无效时
     */
    private function convertToOrderObject(array $shop, array $tradeData)
    {
        // 获取订单ID（支持子订单）
        $orderId     = strval($tradeData["sub_order_id"] ?? $tradeData["tid"] ?? "");
        $mainOrderId = strval($tradeData["main_order_id"] ?? $tradeData["tid"] ?? "");

        // 获取订单基本信息
        $statusCode = $tradeData["status"] ?? "";

        // 获取子订单退款状态
        $refundStatus = $tradeData["refund_status"] ?? "NO_REFUND";

        // 处理金额，优先使用子订单的divide_order_fee作为商家应收金额
        $divideOrderFee = floatval($tradeData["divide_order_fee"] ?? 0);
        $payment        = floatval($tradeData["payment"] ?? 0);
        // 如果是子订单且有divide_order_fee，则使用divide_order_fee作为商家实收
        $receivedPayment = (isset($tradeData["sub_order_id"]) && $divideOrderFee > 0)
            ? $divideOrderFee
            : floatval($tradeData["received_payment"] ?? 0);

        $paymentMethodCode = $tradeData["payment_method"] ?? "";
        $paymentMethod     = $this->normalizePaymentMethod($paymentMethodCode);

        // 根据订单状态和退款状态确定最终状态
        $paymentStatus = "UNKNOWN";

        // 优先判断退款状态
        if ($refundStatus == "SUCCESS") {
            // 退款成功，订单状态为CLOSED
            $paymentStatus = "CLOSED";
        } elseif ($statusCode == "TRADE_CLOSED") {
            // 付款以后用户退款成功，交易自动关闭
            $paymentStatus = "CLOSED";
        } elseif ($statusCode == "TRADE_CLOSED_BY_TAOBAO") {
            // 付款以前，卖家或买家主动关闭交易
            $paymentStatus = "CANCELLED";
        } else {
            // 使用基本状态映射
            $paymentStatus = self::ORDER_STATUS_MAPPING[$statusCode] ?? "UNKNOWN";
        }

        // 解析时间
        $paymentTime = null;
        if (!empty($tradeData["pay_time"])) {
            $paymentTime = DateTime::createFromFormat("Y-m-d H:i:s", $tradeData["pay_time"]);
        }

        $shippingTime = null;
        if (!empty($tradeData["consign_time"])) {
            $shippingTime = DateTime::createFromFormat("Y-m-d H:i:s", $tradeData["consign_time"]);
        }

        // 获取商品名称
        $title = $tradeData["title"] ?? "";

        // 跳过待付款订单（与Python版本逻辑一致）
        if ($paymentStatus == "PENDING") {
            return null;
        }

        // 映射订单状态到最终状态（与Python版本逻辑一致）
        $finalStatus = null;
        if ($paymentStatus == "CANCELLED") {
            $finalStatus = "已取消";
        } elseif ($paymentStatus == "CLOSED" || $refundStatus == "SUCCESS") {
            $finalStatus = "已退款";
        } elseif ($paymentStatus == "COMPLETED") {
            $finalStatus = "已完成";
        } elseif ($paymentStatus == "SHIPPED") {
            $finalStatus = "已发货";
        } elseif ($paymentStatus == "PROCESSING") {
            $finalStatus = "已付款";
        }

        if ($finalStatus === null) {
            return null;
        }

        // 处理金额逻辑（与Python版本一致）
        $merchantReceive = $payment;  // 商家实收金额
        $orderAmount     = $receivedPayment;  // 订单金额

        // 根据订单状态设置退款金额和应收金额
        if (in_array($finalStatus, ["已退款", "已取消"])) {
            $refundAmount       = $merchantReceive;
            $merchantReceivable = 0;
        } else {
            $refundAmount       = $orderAmount - $merchantReceive;
            $merchantReceivable = $merchantReceive;
        }

        // 处理发货时间
        $shippingTimeSec = $shippingTime ? $shippingTime->getTimestamp() : null;

        // 处理交易结束时间
        $endTimeSec = null;
        if (!empty($tradeData["end_time"])) {
            try {
                $endTimeDt = DateTime::createFromFormat("Y-m-d H:i:s", $tradeData["end_time"]);
                if ($endTimeDt) {
                    $endTimeSec = $endTimeDt->getTimestamp();
                }
            } catch (Exception $e) {
                $endTimeSec = null;
            }
        }

        // 获取支付渠道（动态选择）
        $paymentChannel = $this->getPaymentChannel($shop['platform_name'], $paymentMethod);

        // 初始化ERP相关金额（与Python版本一致，暂时设为0）
        $u8c029Amount    = 0;
        $u8c515Amount    = 0;
        $tPlus002Amount  = 0;
        $tPlus008Amount  = 0;
        $billDate        = "";
        $salesOrderTotal = 0;

        // 计算未发货金额
        $unshippedAmount = $merchantReceivable - $salesOrderTotal;

        // 回款相关（与Python版本一致，暂时设为0）
        $receivedAmount = 0;
        $paymentDate    = null;

        // 创建与Python版本入库数据完全对应的订单对象
        $order = [
            'order_id'            => $orderId,
            'platform_type'       => $shop['platform_type'],
            'platform_name'       => $shop['platform_name'],
            'main_order_id'       => $mainOrderId,
            'month'               => date("Y-m", strtotime($tradeData["created"])),
            'order_status'        => $finalStatus,
            'shipping_time'       => $shippingTimeSec,
            'end_time'            => $endTimeSec,
            'order_amount'        => $orderAmount,
            'merchant_receivable' => $merchantReceive,
            'receivable_amount'   => $merchantReceivable,
            'payment_channel'     => $paymentChannel,
            'created_at'          => $tradeData["created"] ?? null,

//            'refund_amount'       => $refundAmount,
//            // ERP相关字段
//            'u8c_029_amount'      => $u8c029Amount,
//            'u8c_515_amount'      => $u8c515Amount,
//            't_plus_002_amount'   => $tPlus002Amount,
//            't_plus_008_amount'   => $tPlus008Amount,
//            'sales_order_total'   => $salesOrderTotal,
//            'sales_order_date'    => $billDate,
//            // 其他字段
//            'unshipped_amount'    => $unshippedAmount,
//            'received_amount'     => $receivedAmount,
//            'payment_date'        => $paymentDate,
//            'pending_payment'     => max(0, $merchantReceivable - $receivedAmount),
//            // 额外信息
//            'title'               => $title,
//            'refund_status'       => $refundStatus,
//            'payment_method'      => $paymentMethod,
        ];
        return $order;
    }


    /**
     * 从订单详情中提取子订单信息
     *
     * @param array $orderDetail 订单详情数据
     * @return array 子订单列表
     */
    private function extractSubOrders(array $orderDetail): array
    {
        $subOrders = [];

        // 检查是否有子订单
        if (isset($orderDetail["orders"]) && isset($orderDetail["orders"]["order"])) {
            $orderList = $orderDetail["orders"]["order"];

            // 获取主订单信息
            $mainOrderId   = $orderDetail["tid"] ?? "";
            $status        = $orderDetail["status"] ?? "";
            $paymentMethod = $orderDetail["payment_method"] ?? "";
            $created       = $orderDetail["created"] ?? "";
            $payTime       = $orderDetail["pay_time"] ?? "";
            $consignTime   = $orderDetail["consign_time"] ?? "";
            $endTime       = $orderDetail["end_time"] ?? "";

            // 处理每个子订单
            foreach ($orderList as $subOrder) {
                // 获取子订单的交易结束时间，优先使用子订单的end_time，如果没有则使用主订单的end_time
                $subEndTime = $subOrder["end_time"] ?? $endTime;

                // 复制主订单信息到子订单
                $subOrderData = [
                    "main_order_id"    => $mainOrderId,
                    "tid"              => $mainOrderId,
                    "status"           => $subOrder["status"] ?? $status,  // 优先使用子订单状态
                    "payment_method"   => $paymentMethod,
                    "created"          => $created,
                    "pay_time"         => $payTime,
                    "consign_time"     => $consignTime,
                    "end_time"         => $subEndTime,  // 使用子订单的交易结束时间
                    // 添加子订单特有信息
                    "sub_order_id"     => $subOrder["oid"] ?? "",
                    "title"            => $subOrder["title"] ?? "",
                    "num"              => $subOrder["num"] ?? 1,
                    "price"            => floatval($subOrder["price"] ?? 0),
                    "payment"          => floatval($subOrder["payment"] ?? 0),  // 子订单金额
                    "divide_order_fee" => floatval($subOrder["divide_order_fee"] ?? 0),  // 分摊后的实付金额
                    "discount_fee"     => floatval($subOrder["discount_fee"] ?? 0),  // 优惠金额
                    "refund_status"    => $subOrder["refund_status"] ?? "NO_REFUND",
                    "total_fee"        => floatval($subOrder["total_fee"] ?? 0)  // 总金额
                ];

                $subOrders[] = $subOrderData;
            }
        }

        // 如果没有子订单，返回原始订单作为子订单
        if (empty($subOrders)) {
            $subOrders[] = $orderDetail;
        }

        return $subOrders;
    }

    /**
     * 调用淘宝开放平台API
     */
    private function callTaobaoApi($shop, string $method, array $params): array
    {
        try {
            $appKey    = $shop['app']['app_key'];
            $appSecret = $shop['app']['app_secret'];
            $session   = $shop['access_token'];

            // 准备请求参数
            $timestamp = date("Y-m-d H:i:s");

            $requestParams = [
                "method"      => $method,
                "app_key"     => $appKey,
                "timestamp"   => $timestamp,
                "format"      => "json",
                "v"           => self::API_VERSION,
                "sign_method" => "md5",
                "session"     => $session
            ];

            // 合并业务参数
            $requestParams = array_merge($requestParams, $params);

            // 计算签名
            $requestParams["sign"] = $this->calculateSign($requestParams, $appSecret);

            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, self::API_URL);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($requestParams));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response  = curl_exec($ch);
            $httpCode  = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            // 检查cURL错误
            if ($curlError) {
                throw new APIConnectionError("淘宝API连接错误: " . $curlError);
            }

            // 检查响应状态
            if ($httpCode !== 200) {
                throw new APIConnectionError("淘宝API请求失败，HTTP状态码: " . $httpCode);
            }

            // 解析响应数据
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("淘宝API返回数据解析失败: " . json_last_error_msg());
            }

            // 检查是否有错误
            if (isset($result["error_response"])) {
                $error     = $result["error_response"];
                $errorCode = $error["code"] ?? "unknown";
                $errorMsg  = $error["msg"] ?? "Unknown error";
                $subCode   = $error["sub_code"] ?? "";
                $subMsg    = $error["sub_msg"] ?? "";

                $errorInfo = "错误码: {$errorCode}, 信息: {$errorMsg}";
                if ($subCode) {
                    $errorInfo .= ", 子错误码: {$subCode}, 子信息: {$subMsg}";
                }

                throw new Exception("淘宝API返回错误: " . $errorInfo);
            }

            return $result;

        } catch (Exception $e) {
            if ($e instanceof APIConnectionError || $e instanceof Exception) {
                throw $e;
            }
            throw new Exception("调用淘宝API失败: " . $e->getMessage());
        }
    }

    /**
     * 获取所有店铺的订单数据
     *
     * @param string $startTime 开始时间 (Y-m-d H:i:s)
     * @param string $endTime 结束时间 (Y-m-d H:i:s)
     * @return array 所有店铺的订单列表
     */
    public function getOrders(string $startTime, string $endTime): array
    {
        $allOrders = [];

        foreach (self::SHOP_CONFIGURATIONS as $shopConfig) {
            try {
                // 为每个店铺获取访问令牌
                $shopConfig['access_token'] = $this->getAccessToken($shopConfig['shop_id']);

                // 获取该店铺的订单并合并到总订单列表
                $shopOrders = $this->getShopOrders($shopConfig, $startTime, $endTime);
                $allOrders  = array_merge($allOrders, $shopOrders);

                echo "从{$shopConfig['platform_name']}获取到 " . count($shopOrders) . " 个订单\n";
            } catch (Exception $e) {
                echo "获取{$shopConfig['platform_name']}订单失败: " . $e->getMessage() . "\n";
            }
        }

        echo "总共获取到 " . count($allOrders) . " 个订单\n";
        return $allOrders;
    }


    /**
     * 标准化支付方式名称
     *
     * @param string $paymentMethodCode 支付方式代码
     * @return string 标准化的支付方式名称
     */
    private function normalizePaymentMethod(string $paymentMethodCode): string
    {
        switch ($paymentMethodCode) {
            case "wx":
                return "微信支付";
            case "alipay":
                return "支付宝支付";
            default:
                return "其他";
        }
    }

    /**
     * 计算淘宝API请求签名
     */
    private function calculateSign(array $params, string $appSecret): string
    {
        // 按字母顺序排序参数
        ksort($params);

        // 拼接参数
        $paramStr = "";
        foreach ($params as $key => $value) {
            if ($value !== null && $value !== "") {
                $paramStr .= $key . $value;
            }
        }

        // 加上app_secret前后缀
        $signStr = $appSecret . $paramStr . $appSecret;

        // 计算MD5
        return strtoupper(md5($signStr));
    }
}