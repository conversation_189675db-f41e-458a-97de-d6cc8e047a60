<?php

return [
    // 默认使用的数据库连接配置
    'default'         => env('database.driver', 'mysql'),

    // 自定义时间查询规则
    'time_query_rule' => [],

    // 自动写入时间戳字段
    // true为自动识别类型 false关闭
    // 字符串则明确指定时间字段类型 支持 int timestamp datetime date
    'auto_timestamp'  => false,

    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',

    // 时间字段配置 配置格式：create_time,update_time
    'datetime_field'  => '',

    // 数据库连接配置信息
    'connections'     => [
        'mysql'                       => [
            // 数据库类型
            'type'            => env('DATABASE_LOCAL.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_LOCAL.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_LOCAL.database', ''),
            // 用户名
            'username'        => env('DATABASE_LOCAL.username', 'root'),
            // 密码
            'password'        => env('DATABASE_LOCAL.password', ''),
            // 端口
            'hostport'        => env('DATABASE_LOCAL.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_LOCAL.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_LOCAL.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_dev'                   => [
            // 数据库类型
            'type'            => env('DATABASE_VMALL.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_VMALL.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_VMALL.database', ''),
            // 用户名
            'username'        => env('DATABASE_VMALL.username', 'root'),
            // 密码
            'password'        => env('DATABASE_VMALL.password', ''),
            // 端口
            'hostport'        => env('DATABASE_VMALL.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_VMALL.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_VMALL.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_vmall_pord'            => [
            // 数据库类型
            'type'            => env('DATABASE_VMALL_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_VMALL_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_VMALL_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_VMALL_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_VMALL_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_VMALL_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_VMALL_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_VMALL_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'database_orders_prod'        => [
            // 数据库类型
            'type'            => env('DATABASE_ORDERS_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_ORDERS_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_ORDERS_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_ORDERS_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_ORDERS_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_ORDERS_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_ORDERS_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_ORDERS_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'database_orders_dev'        => [
            // 数据库类型
            'type'            => env('DATABASE_ORDERS_DEV.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_ORDERS_DEV.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_ORDERS_DEV.database', ''),
            // 用户名
            'username'        => env('DATABASE_ORDERS_DEV.username', 'root'),
            // 密码
            'password'        => env('DATABASE_ORDERS_DEV.password', ''),
            // 端口
            'hostport'        => env('DATABASE_ORDERS_DEV.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_ORDERS_DEV.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_ORDERS_DEV.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'database_data_statistics_prod'        => [
            // 数据库类型
            'type'            => env('DATABASE_DATA_STATISTICS_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_DATA_STATISTICS_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_DATA_STATISTICS_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_DATA_STATISTICS_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_DATA_STATISTICS_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_DATA_STATISTICS_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_DATA_STATISTICS_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_DATA_STATISTICS_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_gs_prod'               => [
            // 数据库类型
            'type'            => env('DATABASE_GS_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_GS_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_GS_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_GS_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_GS_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_GS_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_GS_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_GS_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_marketing_prod'        => [
            // 数据库类型
            'type'            => env('DATABASE_MARKETING_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_MARKETING_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_MARKETING_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_MARKETING_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_MARKETING_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_MARKETING_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_MARKETING_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_MARKETING_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_erp_dev'               => [
            // 数据库类型
            'type'            => env('DATABASE_ERP.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_ERP.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_ERP.database', ''),
            // 用户名
            'username'        => env('DATABASE_ERP.username', 'root'),
            // 密码
            'password'        => env('DATABASE_ERP.password', ''),
            // 端口
            'hostport'        => env('DATABASE_ERP.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_ERP.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_ERP.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_customer_service_prod' => [
            // 数据库类型
            'type'            => env('DATABASE_CUSTOMER_SERVICE_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_CUSTOMER_SERVICE_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_CUSTOMER_SERVICE_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_CUSTOMER_SERVICE_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_CUSTOMER_SERVICE_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_CUSTOMER_SERVICE_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_CUSTOMER_SERVICE_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_CUSTOMER_SERVICE_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_wiki_prod'             => [
            // 数据库类型
            'type'            => env('DATABASE_WIKI_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WIKI_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WIKI_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_WIKI_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WIKI_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WIKI_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WIKI_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WIKI_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ],
        'mysql_commodities'           => [
            // 数据库类型
            'type'            => env('DATABASE_COMMODITIES.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_COMMODITIES.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_COMMODITIES.database', ''),
            // 用户名
            'username'        => env('DATABASE_COMMODITIES.username', 'root'),
            // 密码
            'password'        => env('DATABASE_COMMODITIES.password', ''),
            // 端口
            'hostport'        => env('DATABASE_COMMODITIES.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_COMMODITIES.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_COMMODITIES.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试
        'mysql_commodities_prod'      => [
            // 数据库类型
            'type'            => env('DATABASE_COMMODITIES_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_COMMODITIES_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_COMMODITIES_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_COMMODITIES_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_COMMODITIES_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_COMMODITIES_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_COMMODITIES_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_COMMODITIES_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试
        'dev_mysql_osms_store'        => [
            // 数据库类型
            'type'            => env('DATABASE_OSMS_STORE.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_OSMS_STORE.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_OSMS_STORE.database', ''),
            // 用户名
            'username'        => env('DATABASE_OSMS_STORE.username', 'root'),
            // 密码
            'password'        => env('DATABASE_OSMS_STORE.password', ''),
            // 端口
            'hostport'        => env('DATABASE_OSMS_STORE.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_OSMS_STORE.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_OSMS_STORE.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试

        'pord_mysql_osms_store' => [
            // 数据库类型
            'type'            => env('DATABASE_OSMS_STORE_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_OSMS_STORE_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_OSMS_STORE_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_OSMS_STORE_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_OSMS_STORE_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_OSMS_STORE_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_OSMS_STORE_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_OSMS_STORE_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试

        'pord_mysql_thirdparty_stores' => [
            // 数据库类型
            'type'            => env('DATABASE_THIRDPARTY_STORES_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_THIRDPARTY_STORES_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_THIRDPARTY_STORES_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_THIRDPARTY_STORES_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_THIRDPARTY_STORES_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_THIRDPARTY_STORES_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_THIRDPARTY_STORES_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_THIRDPARTY_STORES_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试
        'pord_mysql_authority'         => [
            // 数据库类型
            'type'            => env('DATABASE_AUTHORITY_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_AUTHORITY_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_AUTHORITY_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_AUTHORITY_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_AUTHORITY_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_AUTHORITY_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_AUTHORITY_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_AUTHORITY_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //商品测试

        'pord_mysql_maidian' => [
            // 数据库类型
            'type'            => env('DATABASE_MAIDIAN_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_MAIDIAN_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_MAIDIAN_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_MAIDIAN_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_MAIDIAN_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_MAIDIAN_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_MAIDIAN_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_MAIDIAN_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //埋点正式
        'pord_mysql_auction' => [
            // 数据库类型
            'type'            => env('DATABASE_AUCTION_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_AUCTION_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_AUCTION_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_AUCTION_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_AUCTION_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_AUCTION_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_AUCTION_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_AUCTION_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //埋点正式
        'dev_mysql_auction'  => [
            // 数据库类型
            'type'            => env('DATABASE_AUCTION_DEV.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_AUCTION_DEV.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_AUCTION_DEV.database', ''),
            // 用户名
            'username'        => env('DATABASE_AUCTION_DEV.username', 'root'),
            // 密码
            'password'        => env('DATABASE_AUCTION_DEV.password', ''),
            // 端口
            'hostport'        => env('DATABASE_AUCTION_DEV.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_AUCTION_DEV.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_AUCTION_DEV.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //埋点正式
        'pord_mysql_user'    => [
            // 数据库类型
            'type'            => env('DATABASE_USER_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_USER_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_USER_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_USER_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_USER_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_USER_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_USER_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_USER_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //用户正式


        'dev_mysql_wms_stock_1'  => [
            // 数据库类型
            'type'            => env('DATABASE_WMS_STOCK_1.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WMS_STOCK_1.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WMS_STOCK_1.database', ''),
            // 用户名
            'username'        => env('DATABASE_WMS_STOCK_1.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WMS_STOCK_1.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WMS_STOCK_1.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WMS_STOCK_1.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WMS_STOCK_1.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //萌芽酒云测试
        'prod_mysql_wms_stock_1' => [
            // 数据库类型
            'type'            => env('DATABASE_WMS_STOCK_1_PROD.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WMS_STOCK_1_PROD.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WMS_STOCK_1_PROD.database', ''),
            // 用户名
            'username'        => env('DATABASE_WMS_STOCK_1_PROD.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WMS_STOCK_1_PROD.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WMS_STOCK_1_PROD.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WMS_STOCK_1_PROD.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WMS_STOCK_1_PROD.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //萌芽酒云正式
        'dev_mysql_wms_stock_2'  => [
            // 数据库类型
            'type'            => env('DATABASE_WMS_STOCK_2.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WMS_STOCK_2.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WMS_STOCK_2.database', ''),
            // 用户名
            'username'        => env('DATABASE_WMS_STOCK_2.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WMS_STOCK_2.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WMS_STOCK_2.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WMS_STOCK_2.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WMS_STOCK_2.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //萌芽科技测试
        'dev_mysql_wms_stock_3'  => [
            // 数据库类型
            'type'            => env('DATABASE_WMS_STOCK_3.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WMS_STOCK_3.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WMS_STOCK_3.database', ''),
            // 用户名
            'username'        => env('DATABASE_WMS_STOCK_3.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WMS_STOCK_3.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WMS_STOCK_3.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WMS_STOCK_3.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WMS_STOCK_3.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //萌芽松鸽测试
        'dev_mysql_wms_stock_4'  => [
            // 数据库类型
            'type'            => env('DATABASE_WMS_STOCK_4.type', 'mysql'),
            // 服务器地址
            'hostname'        => env('DATABASE_WMS_STOCK_4.hostname', '127.0.0.1'),
            // 数据库名
            'database'        => env('DATABASE_WMS_STOCK_4.database', ''),
            // 用户名
            'username'        => env('DATABASE_WMS_STOCK_4.username', 'root'),
            // 密码
            'password'        => env('DATABASE_WMS_STOCK_4.password', ''),
            // 端口
            'hostport'        => env('DATABASE_WMS_STOCK_4.hostport', '3306'),
            // 数据库连接参数
            'params'          => [],
            // 数据库编码默认采用utf8
            'charset'         => env('DATABASE_WMS_STOCK_4.charset', 'utf8'),
            // 数据库表前缀
            'prefix'          => env('DATABASE_WMS_STOCK_4.prefix', ''),

            // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
            'deploy'          => 0,
            // 数据库读写是否分离 主从式有效
            'rw_separate'     => false,
            // 读写分离后 主服务器数量
            'master_num'      => 1,
            // 指定从服务器序号
            'slave_no'        => '',
            // 是否严格检查字段是否存在
            'fields_strict'   => true,
            // 是否需要断线重连
            'break_reconnect' => false,
            // 监听SQL
            'trigger_sql'     => env('app_debug', true),
            // 开启字段缓存
            'fields_cache'    => false,
        ], //萌芽木兰朵测试
        // 更多的数据库配置信息
    ],
];
