<?php

namespace app\model;

class Orders extends BaseModel
{
    
    


    
    #region time 获取器和编辑器
    public function setTimeAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s',$value) : null;
    }
    #endregion
    

}