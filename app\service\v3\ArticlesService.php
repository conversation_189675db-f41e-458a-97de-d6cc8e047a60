<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Articles;
use app\validate\ArticlesValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 微信公众号帖子
 * Class ArticlesService
 * @package app\service\v3
 */
class ArticlesService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Articles;
        $this->validate    = ArticlesValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/03/30 16:14
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ wxid wxid
            if (isset($param['wxid']) && strlen($param['wxid']) > 0) {
                $query->where('wxid', "=", $param['wxid']); //wxid
            }
            #endregion

        };
    }


    /**
     * @方法描述: 微信公众号帖子 Embedding
     * <AUTHOR>
     * @Date 2023/3/30 16:28
     * @param $param
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function embedding($param)
    {

        $start = 25;
        $limit = 1000;

        $list = $this->model->limit($start, $limit)->select()->toArray();
        $err  = [];
        foreach ($list as $item) {
            $mq_data = [
                'eid'      => $item['wxid'],
                'text'     => strip_tags($item['content']),
                'category' => 'wine_wx_articles',
            ];

            $res = $this->queuePush($mq_data);
            if ($res !== true) {
                $err[] = $item['wxid'];
            }
        }

        print_r($err);
        die;
    }


    /**
     * @方法描述: 微信公众号帖子 embedding 加入队列
     * <AUTHOR>
     * @Date 2023/3/30 16:29
     * @param $data
     * @return true
     * @throws Exception
     */
    protected function queuePush($data)
    {
        $header[] = 'vinehoo-client:embedding';

        $url = env('ITEM.QUEUE_URL');

        $code   = httpCurl($url, 'post', json_encode([
            'exchange_name' => 'openai',
            'routing_key'   => 'openai.embeddings',
            'data'          => base64_encode(json_encode($data)),
        ]), 3, $header);
        $result = json_decode($code, true);

        Log::write('queuePush  ' . $code);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('更新加入队列错误' . $code);
        }
        return true;
    }


}



