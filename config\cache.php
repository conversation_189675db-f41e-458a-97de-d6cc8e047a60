<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => 'redis',

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        // 更多的缓存连接
        'redis' => [
            // 驱动方式
            'type'     => env("CACHE.DRIVER", "redis"),
            // 缓存前缀
            'prefix'   => env("CACHE.PREFIX", "vinehoo."),
            //地址
            'host'     => env("CACHE.HOST", "127.0.0.1"),
            //  端口号
            'port'     => env("CACHE.PORT", 6379),
            //  密码
            'password' => env("CACHE.PASSWORD", "vh@123"),
            //  默认缓存时间
            'expire'   => 0,
            //db仓库
            'select'   => env("CACHE.DB", 1),
        ],
        'cache_vmall_dev' => [
            // 驱动方式
            'type'     => env("CACHE_VMALL_DEV.DRIVER", "redis"),
            // 缓存前缀
            'prefix'   => env("CACHE_VMALL_DEV.PREFIX", "vinehoo."),
            //地址
            'host'     => env("CACHE_VMALL_DEV.HOST", "127.0.0.1"),
            //  端口号
            'port'     => env("CACHE_VMALL_DEV.PORT", 6379),
            //  密码
            'password' => env("CACHE_VMALL_DEV.PASSWORD", "vh@123"),
            //  默认缓存时间
            'expire'   => 0,
            //db仓库
            'select'   => env("CACHE_VMALL_DEV.DB", 1),
        ],
        // v3用户redis缓存
        'redis_user'   =>  [
            // 驱动方式
            'type'   => 'redis',
            // 服务器地址
            'host'       => env( 'cache.host','127.0.0.1'),
            'password'   => env( 'cache.password',''),
            'port'       => env( 'cache.port','6379'),
            'select'     => 11,
            'prefix'     => env('cache.prefix',''),
            'serialize'  => ['trim','trim']
        ],
    ],
];
