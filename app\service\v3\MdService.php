<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Md;
use app\validate\MdValidate;
use League\HTMLToMarkdown\HtmlConverter;
use think\facade\Db;
use think\facade\Log;

/**
 * 埋点
 * Class MdService
 * @package app\service\v3
 */
class MdService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Md;
        $this->validate    = MdValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/01 16:52
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }


    public function exportUserPhone($param)
    {
        $uids = Db::connect('pord_mysql_maidian')->name('report_default')
            ->where('is_login', 1)
            ->whereTime('created_time', '>=', '2023-05-01 00:00:00')
//            ->limit(0, 10)
            ->group('uid')
            ->column('uid');

        $uids = array_unique($uids);

        $uid_group       = array_chunk($uids, 1000);
        $uid_group_count = count($uid_group);

        $data = $enc_err_data = [];

        $i = 1;
        foreach ($uid_group as $uid_items) {
            $enc_datum = Db::connect('pord_mysql_user')->name('user')
                ->where('uid', 'in', $uid_items)
                ->column('telephone');

            $dec_data = \Curl::cryptionDeal($enc_datum);
            if (empty($dec_data)) {
                $enc_err_data[] = $enc_datum;
                $item_res       = 'err';
            } else {
                $data     = array_merge($data, array_values($dec_data));
                $item_res = 'success';
            }

            print_r("{$i}/{$uid_group_count} {$item_res}" . PHP_EOL);
            $i++;
        }

        return $this->exportExcel([
            'filename'   => '5月1日至今登录过用户手机号.xlsx',
            'sheet_name' => '5月1日至今登录过用户手机号',
            'header'     => ['手机号'],
            'data'       => array_values(array_unique($data)),
        ]);

    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            if (is_array($ret_data)) {
                $export_data[] = array_values($ret_data);
            } else {
                $export_data[] = [$ret_data];
            }
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }


    /**
     * @方法描述: 让AI选择标签
     * <AUTHOR>
     * @Date 2025/8/29 15:45
     * @param $param
     */
    public function aiSelectLabels($param)
    {
        $desc = "让AI选择标签";

        $in_data = readExcel('glxcwznr.xlsx', '', [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            1 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            2 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP, // 将第一列设置为字符串类型
        ]);
        $list    = $in_data['sheet'][1]['list'];
        $header  = $list[0];
        unset($list[0]);
        foreach ($list as $i => $item) {
            $list[$i][2] = trim($list[$i][2]);
        }
        $list = array_column($list, '2');


        $cates = Db::connect('database_orders_prod')->table('vh_news.vh_article_cate')
            ->column('name', 'id');

        $articles = Db::connect('database_orders_prod')->table('vh_news.vh_article')
            ->whereIn('title', $list)
            ->column('cate_id,title,add_time', 'title');

        $titles = array_chunk($articles, 50);
        $total  = count($titles);

        // 使用并发方式调用AI接口
        $deepseek = $this->aiSelectLabelsConcurrent($titles);

        $deepseek = array_column($deepseek, 'type', 'title');

        Log::write("Deepseek:" . json_encode($deepseek));

        $data[$desc][] = ['原标签', "AI选择的标签", '标题', '创建时间'];
        foreach ($articles as $article) {
            $data[$desc][] = [$cates[$article['cate_id']] ?? '', $deepseek[$article['title']] ?? '', $article['title'], $article['add_time']];
        }


        print_r(outExcel($data, $desc));
        die;
    }

    /**
     * @方法描述: 并发调用AI接口选择标签
     * <AUTHOR>
     * @Date 2025/8/29 16:00
     * @param array $titles 分组后的标题数组
     * @return array 合并后的AI结果
     */
    private function aiSelectLabelsConcurrent($titles)
    {
        $total    = count($titles);
        $deepseek = [];

        // 准备所有请求参数
        $requests = [];
        foreach ($titles as $index => $title_g) {
            $requests[$index] = [
                'model'    => 'deepseek-chat',
                'messages' => [
                    [
                        'role'    => 'system',
                        'content' => <<<str
新闻,科普,饮用,美食,文旅,人物,选酒;
下面我给你许多个文章标题, 需要你根据标题内容, 匹配前面7个里面最合适的标签;
标签,分割的;
 返回内容要求:  json格式  每个元素包含 title(标题) 和 type(类型), 返回的json的长度应当与我传给你的 json 长度一致, 返回内容直接就是JSON格式的字符串, 不要包含其他的字符
str
                    ],
                    [
                        'role'    => 'user',
                        'content' => json_encode($title_g, JSON_UNESCAPED_UNICODE),
                    ],
                ]
            ];
        }

        // 使用并发方法调用AI接口，设置并发数为5
        $results = \Curl::deepseekConcurrent($requests, 5);

        // 处理结果
        $completed = 0;
        foreach ($results as $index => $response) {
            $completed++;
            if ($response) {
                $aiResult = json_decode($response, true);
                if ($aiResult && is_array($aiResult)) {
                    $deepseek = array_merge($deepseek, $aiResult);
                }
            }
            // 显示进度
            print_r("{$completed}/{$total} DONE" . PHP_EOL);
        }

        return $deepseek;
    }


    public function aiHtmlToMarkdown($param)
    {
        $desc = "富文本转换MarkdownAi";

        $in_data = readExcel('glxcwznr.xlsx', '', [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            1 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            2 => \Vtiful\Kernel\Excel::TYPE_STRING, // 将第一列设置为字符串类型
            3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP, // 将第一列设置为字符串类型
        ]);
        $list    = $in_data['sheet'][1]['list'];
        $header  = $list[0];
        unset($list[0]);
        $titles = array_column($list, '2');
        $titles = array_chunk($titles, 100);


        foreach ($titles as $title_group) {
            $articles = Db::connect('database_orders_prod')->table('vh_news.vh_article')
                ->whereIn('title', $title_group)
                ->whereIn('md_info', '')
                ->column('id,title,info', 'title');

            if (empty($articles)) {
                continue;
            }

            // 使用并发方式处理HTML转Markdown
            $this->processHtmlToMarkdownConcurrent($articles);
        }

        print_r('end');die;
    }

    /**
     * @方法描述: 并发处理HTML转Markdown
     * <AUTHOR>
     * @Date 2025/8/29 16:30
     * @param array $articles 文章数组
     */
    private function processHtmlToMarkdownConcurrent($articles)
    {
        $total = count($articles);
        print_r("开始处理 {$total} 篇文章的HTML转Markdown..." . PHP_EOL);

        // 准备所有请求参数
        $requests = [];
        $articleMap = []; // 用于映射请求索引到文章ID

        foreach ($articles as $index => $article) {
            $requests[$index] = [
                'model'    => 'deepseek-chat',
                'messages' => [
                    [
                        'role'    => 'system',
                        'content' => <<<str
将下面HTML富文本转换为Markdown代码。
理解文章并识别出文章中的文章标题，一级标题，二级标题，图片（包括图注），无序列表，有序列表，引用文本

## 要求
文章标题 使用 `#`
一级标题 使用 `##`
二级标题 使用 `###`

## 输出格式
```markdown
<markdown>
````
str
                    ],
                    [
                        'role'    => 'user',
                        'content' => $article['info'],
                    ],
                ]
            ];
            $articleMap[$index] = $article['id'];
        }

        // 使用并发方法调用AI接口，设置并发数为3（HTML转换通常内容较大，减少并发数）
        $results = \Curl::deepseekConcurrent($requests, 10);

        // 处理结果并更新数据库
        $successCount = 0;
        $failCount = 0;

        foreach ($results as $index => $markdownContent) {
            $articleId = $articleMap[$index];

            if ($markdownContent) {
                // 更新数据库
                $res = Db::connect('database_orders_prod')->table('vh_news.vh_article')
                    ->where('id', $articleId)
                    ->update(['md_info' => $markdownContent]);

                if ($res) {
                    $successCount++;
                    print_r("文章ID: {$articleId} - 转换成功" . PHP_EOL);
                } else {
                    $failCount++;
                    print_r("文章ID: {$articleId} - 数据库更新失败" . PHP_EOL);
                }
            } else {
                $failCount++;
                print_r("文章ID: {$articleId} - AI转换失败" . PHP_EOL);
            }
        }

        print_r("批次处理完成: 成功 {$successCount} 篇, 失败 {$failCount} 篇" . PHP_EOL);
    }




    /**
     * @方法描述: 将富文本HTML转换为Markdown格式 (使用league/html-to-markdown库)
     * <AUTHOR>
     * @Date 2024/12/19 10:00
     * @param string $html 富文本HTML内容
     * @param array $options 转换选项配置
     * @return string 转换后的Markdown内容
     */
    public function convertHtmlToMarkdown($param)
    {
        $param = [
            'html' => '',
            'id'   => 10405,
        ];

        $html    = $param['html'];
        $options = [];

        $html = Db::connect('database_orders_prod')->table('vh_news.vh_article')->where('id', $param['id'])->value('info');

        $res = \Curl::deepseek([
            'model'    => 'deepseek-chat',
            'messages' => [
                [
                    'role'    => 'system',
                    'content' => <<<str
将下面HTML富文本转换为Markdown代码。
理解文章并识别出文章中的文章标题，一级标题，二级标题，图片（包括图注），无序列表，有序列表，引用文本

## 要求
文章标题 使用 `#`
一级标题 使用 `##`
二级标题 使用 `###`

## 输出格式
```markdown
<markdown>
````
str
                    ,
                ],
                [
                    'role'    => 'user',
                    'content' => $html,
                ],
            ]
        ]);

        print_r($res);
        die;


        try {
            // 默认配置
            $defaultOptions = [
                'header_style'      => 'atx',        // 使用 # 风格的标题
                'bold_style'        => '**',           // 粗体使用 **
                'italic_style'      => '*',          // 斜体使用 *
                'list_item_style'   => '-',       // 无序列表使用 -
                'preserve_comments' => false,   // 不保留HTML注释
                'strip_tags'        => false,          // 不删除不支持的标签
                'remove_nodes'      => 'script style', // 移除script和style标签
            ];

            // 合并用户配置
            $config = array_merge($defaultOptions, $options);

            // 创建转换器实例
            $converter = new HtmlConverter($config);

            // 执行转换
            $markdown = $converter->convert($html);

            // 清理多余的空行
            $markdown = preg_replace('/\n{3,}/', "\n\n", $markdown);

            print_r(trim($markdown));
            die;

        } catch (\Exception $e) {
            // 如果转换失败，记录错误并返回原始HTML（去除标签）
            return strip_tags($html);
        }
    }


}
