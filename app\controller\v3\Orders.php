<?php

namespace app\controller\v3;

use app\BaseController;
use app\Request;
use app\service\es\Es;
use app\service\v3\WorkService;
use think\App;
use think\facade\Db;
use app\service\v3\OrdersService;


/**
 * 工单
 * Class Orders
 * @package app\controller\v3
 */
class Orders extends BaseController
{

    public function initialize()
    {
        $this->service = new OrdersService;
    }

    public function exportOfflineOrder(Request $request)
    {
        $list = Db::connect('database_orders_prod')->name('offline_order')->alias('a')
            ->join('order_main b', 'a.main_order_id = b.id')
            ->where('a.document_type', 2)
            ->where('a.customer', "LIKE", "%快团团%")
            ->field([
                'a.id',
                'a.sub_order_no',
                'a.items_info',
                'a.voucher_date',
                'a.customer',
                'a.express_number',
                'a.operator',
                'a.warehouse',
                'a.push_t_status',
                'a.push_wms_status',
                'b.consignee',
                'b.consignee_phone',
                'b.address',
                'a.payment_amount',
                'b.main_order_no',
            ])
            ->select()->toArray();

        $push_t_status   = [0 => '未推送', 1 => '推送成功', 2 => '推送失败', 3 => '不推送'];
        $push_wms_status = [0 => '未推送', 1 => '推送成功', 2 => '推送失败', 3 => '不推送'];


        $enc_data = [];
        foreach ($list as $var) {
            $enc_data[] = $var['consignee'];
            $enc_data[] = $var['consignee_phone'];
        }
        $dec_data = \Curl::cryptionDeal($enc_data);

        $operators = \Curl::adminInfo(['admin_id' => implode(',', array_unique(array_column($list, 'operator')))]);
        $operators = array_column($operators, null, 'id');

        $header = [
            '单据编号',
            '单据日期',
            '客户',
            '运单号',
            'ERP',
            '发货仓',
            '制单人',
            '仓库',
            '联系人',
            '联系电话',
            '收货地址',
            '订单金额',
            '简码',
            '数量',
            '中文名称',
            '单价',
            '合计金额',
            '订单',
        ];
        $data   = [];
        foreach ($list as $item) {
            $items_infos_arr = explode(',', $item['items_info']);

            foreach ($items_infos_arr as $valu) {
                $temp_arr = explode('*', $item['items_info']);
                //条码1
                //简码1
                //商品数量
                //含税单价
                //是否赠品：0-否 1-是
                //协议价
                //产品名称
                //年份
                //销售单位
                //萌牙库存
                //erp库存
                //规格型号
                //含税总价
                //协议价类型：0-未知 1-客户价 2-批发价）多个逗号分隔

                $data[] = [
                    $item['sub_order_no'],
                    date("Y-m-d", $item['voucher_date']),
                    $item['customer'],
                    $item['express_number'],
                    $push_t_status[$item['push_t_status']] ?? $item['push_t_status'],
                    $push_wms_status[$item['push_wms_status']] ?? $item['push_wms_status'],
                    $operators[$item['operator']]['realname'] ?? $item['operator'],
                    $item['warehouse'],
                    $dec_data[$item['consignee']] ?? $item['consignee'],
                    $dec_data[$item['consignee_phone']] ?? $item['consignee_phone'],
                    $item['address'],
                    $item['payment_amount'],
                    $temp_arr[1],
                    $temp_arr[2],
                    $temp_arr[6],
                    $temp_arr[3],
                    bcmul($temp_arr[3], $temp_arr[2], 2),
                    $item['main_order_no'],
                ];

            }
        }

        return $this->exportExcel([
            'filename'   => '线下订单快团团.xlsx',
            'sheet_name' => '线下订单快团团',
            'header'     => $header,
            'data'       => $data,
        ]);

    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }


    /**
     * 导出购买过的 在酒云网购买过商品的用户
     * 导出4月1日至今，在酒云网购买过商品的用户。需要的字段：手机号。   用于营销短信发送
     */
    public function purchasedUser($param = [])
    {
        try {

            $enc_data = Db::connect('database_orders_prod')->name('order_main')
                ->whereTime('created_time', '>=', '2023-4-1 00:00:00') //4月1日至今
                ->where('uid', '<>', 0) //剔除非中台订单
                ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                ->field('consignee_phone')
                ->distinct(true)
                ->select()->toArray();

            $enc_data = array_values(array_unique(array_column($enc_data, 'consignee_phone')));

            $export_data = [];

            $enc_err_data = [];

            $enc_data = array_chunk($enc_data, 500);

            foreach ($enc_data as $enc_datum) {
                $dec_data = \Curl::cryptionDeal($enc_datum);
                if (empty($dec_data)) {
                    $enc_err_data[] = $enc_datum;
                } else {
                    foreach ($enc_datum as $enc_key) {
                        $export_data[] = [$enc_key, $dec_data[$enc_key] ?? ''];
                    }
                }
            }

            foreach ($enc_err_data as $enc_err_datum) {
                $export_data[] = ['ERROR', json_encode($enc_err_datum)];
            }


            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('4月1日至今，在酒云网购买过商品的用户.xlsx', 'sheet_1');

            $filePath = $excel
                ->header(['手机号(加密)', '手机号'])
                ->data($export_data)
                ->output();

            return $filePath;
        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }

    /**
     * @方法描述: 售前客服效率导出
     * <AUTHOR>
     * @Date 2023/11/6 14:50
     * @param $param
     * @return string|void
     */
    public function preSalesEfficiency($param = [])
    {
        try {

            $uids = [];

            $allocation_records = Db::connect('database_orders_prod')->name('presales_allocation_user')
                ->whereBetweenTime('created_time', '2023-10-1 00:00:00', '2023-10-31 23:59:59') //10月份
                ->where('type', 1) //用户类型：1-新用户 2-沉默用户
                ->where('admin_id|user_id', '<>', null)
                ->field('id,admin_id,user_id')
                ->select()->toArray(); // 分配记录

            $orders = Db::connect('database_orders_prod')->name('share_order_log')
                ->whereBetweenTime('created_time', '2023-10-1 00:00:00', '2023-10-31 23:59:59') //10月份
                ->where('source_user|uid', '<>', null)
                ->field('id,source_user,uid,share_name,sub_order_no,order_type')
                ->select()->toArray();
            $orders = array_column($orders, null, 'sub_order_no');

            $order_amounts = [];

            $orders_user_group = $orders_type_group = [];
            foreach ($orders as $order) {
                $uids[]                                    = $order['uid'];
                $orders_type_group[$order['order_type']][] = $order;
            }
            $order_models = [
                0 => Db::connect('database_orders_prod')->name('flash_order'), //闪购
                2 => Db::connect('database_orders_prod')->name('cross_order'), //跨境
            ];
            foreach ($orders_type_group as $order_type => $ot_list) {
                $order_model     = $order_models[$order_type];
                $order_type_list = $order_model->where('sub_order_no', 'in', array_column($ot_list, 'sub_order_no'))
                    ->field('sub_order_no,payment_amount,refund_money')
                    ->select()->toArray();

                foreach ($order_type_list as $order_am) {
                    $amount = bcsub($order_am['payment_amount'], $order_am['refund_money'], 2);
                    if ($amount > 0) {
                        $ori_order                                = $orders[$order_am['sub_order_no']];
                        $ori_order['amount']                      = $amount;
                        $order_amounts[$order_am['sub_order_no']] = $ori_order;
                    }
                }
            }
            foreach ($order_amounts as $order) {
                $orders_user_group[$order['source_user']][] = $order;
            }


            $admin_ids        = array_values(array_unique(array_column($allocation_records, 'admin_id')));
            $admins           = \Curl::adminInfo([
                'admin_id' => implode(',', $admin_ids),
            ]);
            $admins           = array_column($admins, 'realname', 'id');
            $allocation_group = [];


            foreach ($allocation_records as $k => $v) {
                $uids[]                             = $v['user_id'];
                $v['admin_name']                    = $admins[$v['admin_id']];
                $allocation_group[$v['admin_id']][] = $v;
            }

            $table1        = [
                ['业务员', '单量', '总金额'],
            ];
            $table2        = [
                ['业务员', '转换人数', '接收人数', '转换率'],
            ];
            $table3        = [];
            $table3_header = ['已转换的用户昵称', '定向转换单量', '销售额', '用户类型'];
            $user_types    = Db::connect('database_orders_prod')->name('presales_allocation_user')->column('type', 'user_id');


            $uids = array_values(array_unique($uids));

            $uids_g = array_chunk($uids, 100);
            $users  = [];
            foreach ($uids_g as $uid_sub) {
                $sub_users = \Curl::getUserInfoList(implode(',', $uid_sub));
                $users     = array_merge($users, $sub_users);
            }

            $users = array_column($users, 'nickname', 'uid');

            $type_txt = [1 => '新用户', 2 => '沉默用户', 3 => '老用户'];

            foreach ($allocation_group as $e_admin_id => $e_item) {
                $admin_order_group = $orders_user_group[$e_admin_id] ?? [];

                $admin_name = $admins[$e_admin_id];
                $dl         = count($admin_order_group); //单量
                $zje        = round(array_sum(array_column($admin_order_group, 'amount')), 2); //总金额
                $zhrs       = count(array_unique(array_column($admin_order_group, 'uid'))); //转换人数
                $jsrs       = count(array_unique(array_column($e_item, 'user_id'))); //接收人数
                $zhl        = bcmul(bcdiv($zhrs, $jsrs, 6), '100', 4) . '%'; //转换率

                $table1[] = [$admin_name, $dl, $zje];
                $table2[] = [$admin_name, $zhrs, $jsrs, $zhl];

                $tab3 = [$table3_header];

                $user_group_orders = [];
                foreach ($admin_order_group as $item) {
                    $user_group_orders[$item['uid']][] = $item;
                }

                foreach ($user_group_orders as $u_id => $u_group) {
                    $tab3[] = [
                        $users[$u_id] ?? $u_id,
                        count($u_group),
                        round(array_sum(array_column($u_group, 'amount')), 2),
                        $type_txt[$user_types[$u_id] ?? 3] ?? '未知的'
                    ];
                }

                $table3[$admin_name] = $tab3;
            }


            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('售前客服效率导出.xlsx', 'table1');

            $excel = $excel
                ->header(array_shift($table1))
                ->data($table1)
                ->addSheet('table2')
                ->header(array_shift($table2))
                ->data($table2);

            foreach ($table3 as $sheet => $sub_table_3) {
                $excel = $excel->addSheet($sheet)
                    ->header(array_shift($sub_table_3))
                    ->data($sub_table_3);
            }

            $filePath = $excel->output();

            return $filePath;
        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }


    /**
     * @方法描述:为ICON VINEHOO售票发送短信，申请广州用户导出 说明：1.广州用户，2.用户3年内有消费的非沉默用户；3.只需要导出手机号即可
     * <AUTHOR>
     * @Date 2023/11/6 14:50
     * @param $param
     * @return string|void
     */
    public function gzUser($param = [])
    {
        $citys = ['233' => '广州', '235' => '深圳'];

        try {
            $data = [];

            $silence_uids = Db::connect('mysql_marketing_prod')->name('special_activity_target_user')//沉默用户
            ->where('special_activity_id', '999999')->column('vh_uid');  //沉默用户

            $export_data = [];

            foreach ($citys as $city_id => $city_name) {
                //1.指定城市用户
                $uids = Db::connect('pord_mysql_user')->name('user_address')
                    ->where('city_id', $city_id)
                    ->where('is_delete', 0)
                    ->where('is_default', 1)
                    ->where('uid', 'not in', $silence_uids)
                    ->column('uid');

                $start_time   = date('Y-m-d H:i:s', strtotime('-3 year')); //三年前
                $consume_uids = Db::connect('database_orders_prod')->name('order_main')
                    ->whereTime('created_time', '>=', strtotime($start_time)) //至今
                    ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ->where('uid', 'in', $uids)
                    ->group('uid')
                    ->column('uid'); //3年消费过的用户
                $enc_data     = Db::connect('pord_mysql_user')->name('user')
                    ->where('uid', 'in', $consume_uids)
                    ->where('is_delete', 0)//未删除的用户
                    ->column('telephone');
                $enc_data     = array_chunk($enc_data, 500);


                $enc_err_data = [];

                foreach ($enc_data as $enc_datum) {
                    $dec_data = \Curl::cryptionDeal($enc_datum);
                    if (empty($dec_data)) {
                        $enc_err_data[$city_name][] = $enc_datum;
                    } else {
                        foreach ($enc_datum as $enc_key) {
                            $export_data[$city_name][] = [$dec_data[$enc_key] ?? ''];
                        }
                    }
                }

                foreach ($enc_err_data as $enc_err_datum) {
                    $export_data[$city_name] = [json_encode($enc_err_datum)];
                }
            }


            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('广州深圳3年有消费非沉默用户手机号.xlsx', '广州');

            $filePath = $excel
                ->data($export_data['广州'])
                ->addSheet('深圳')
                ->data($export_data['深圳'])
                ->output();

            return $filePath;
        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }

    //售前客服绩效数据导出
    public function jxUser($param = [])
    {

        $user = [
            '315' => '肖恩静',
            '381' => '周松',
            '210' => '梅林丽',
            '408' => '金爱梅',
        ];


        $file  = '444.xls';
        $arr   = $this->readExcel($file);
        $u_arr = [];
        foreach ($arr as $arr_item) {
            $u_arr[$arr_item[1]] = strtotime($arr_item[0]);
        }
        $uids     = array_keys($u_arr);
        $admin_id = 408;


        try {
            $data = [];

//            $allocation_records = Db::connect('database_orders_prod')->name('presales_allocation_user')
//                ->where('admin_id', $admin_id)
//                ->where('user_id', 'in', $uids)
//                ->field('id,type,admin_id,user_id,created_time')
////                ->limit(0,100) // todo .............................
//                ->select()->toArray(); // 分配记录

            $new_users = $cm_users = [];
//            foreach ($allocation_records as $allocation_record) {
//                if ($allocation_record['type'] == 1) { //用户类型：1-新用户 2-沉默用户
//                    $new_users[] = $allocation_record;
//                } else {
//                    $cm_users[] = $allocation_record;
//                }
//            }

            foreach ($uids as $aaaauid) {
                $new_users[] = [
                    'type'    => 1,
                    'user_id' => $aaaauid,
//                    id,,admin_id,,created_time
                ];
            }


            $new_users2 = [];
            //过滤新用户
            foreach ($new_users as $newUser) {
                $uid = Db::connect('database_orders_prod')->name('order_main')
                    ->whereTime('created_time', '<=', date('Y-m-d H:i:s', $u_arr[$newUser['user_id']]))
                    ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ->where('uid', 'in', $newUser['user_id'])
                    ->find();
                if ($uid == null) {
                    //未下过单， 有效用户
                    $new_users2[] = $newUser;
                }
            }

            //查询有效用户的订单
            $orders = Db::connect('database_orders_prod')->name('order_main')
                ->whereTime('created_time', '<', '2023-10-31 23:59:59')
                ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                ->where('uid', 'in', array_column($new_users2, 'user_id'))
                ->order('uid', 'DESC')
                ->field('uid,main_order_no,payment_amount,created_time,id')
                ->select()->toArray();

            $main_order_ids = array_column($orders, 'id');

            $labels_main_ids = Db::connect('database_orders_prod')->name('order_source_log')
                ->where('source_event', 'share')
                ->where('source_user', '<>', null)
                ->where('main_order_id', 'in', $main_order_ids)
                ->column('main_order_id');

            //查询子单退款金额
            $sub_orders = Es::name(Es::ORDERS)->where([
                ['main_order_id', 'in', $main_order_ids],
                ['refund_status', '=', 2],
            ])
                ->field('main_order_id,payment_amount,refund_money')
                ->select()->toArray();

            $refund_sub_orders = [];
            foreach ($sub_orders as $sub_order) {
                if (isset($refund_sub_orders[$sub_order['main_order_id']])) {
                    $refund_sub_orders[$sub_order['main_order_id']] = bcadd($refund_sub_orders[$sub_order['main_order_id']], $sub_order['payment_amount'], 2);
                } else {
                    $refund_sub_orders[$sub_order['main_order_id']] = $sub_order['payment_amount'];
                }
            }

            $data[$user[$admin_id]][] = ['UID', '主订单号', '订单金额', '订单创建时间', '标识', '用户类型'];

            foreach ($orders as $order) {
                $data[$user[$admin_id]][] = [$order['uid'], $order['main_order_no'],
                    bcsub($order['payment_amount'], ($refund_sub_orders[$order['id']] ?? '0'), 2)
                    , date('Y-m-d H:i:s', $order['created_time']),
                    in_array($order['id'], $labels_main_ids) ? '分享' : '否'
                    , '新用户'];
            }


            //沉默用户 添加之前 或者 9月 30 日 有下单就不算
            $cm_users2 = [];
            $j30t      = 30 * 24 * 60 * 60;
            $day930    = strtotime('2023-09-30 23:59:59');
            foreach ($cm_users as $cmUser) {
                $uid = Db::connect('database_orders_prod')->name('order_main')
                    ->whereBetweenTime('created_time', date('Y-m-d H:i:s', $u_arr[$cmUser['user_id']] - $j30t), date('Y-m-d H:i:s', max($day930, $u_arr[$cmUser['user_id']])))
                    ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                    ->where('uid', 'in', $cmUser['user_id'])
                    ->find();
                if ($uid == null) {
                    //未下过单， 有效用户
                    $cm_users2[] = $cmUser;
                }
            }

            //查询有效用户的订单
            $orders = Db::connect('database_orders_prod')->name('order_main')
                ->whereBetweenTime('created_time', '2023-10-01 00:00:00', '2023-10-31 23:59:59')
                ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                ->where('uid', 'in', array_column($cm_users2, 'user_id'))
                ->order('uid', 'DESC')
                ->field('uid,main_order_no,payment_amount,created_time,id')
                ->select()->toArray();

            $main_order_ids = array_column($orders, 'id');

            $labels_main_ids = Db::connect('database_orders_prod')->name('order_source_log')
                ->where('source_event', 'share')
                ->where('source_user', '<>', null)
                ->where('main_order_id', 'in', $main_order_ids)
                ->column('main_order_id');

            //查询子单退款金额
            $sub_orders = Es::name(Es::ORDERS)->where([
                ['main_order_id', 'in', $main_order_ids],
                ['refund_status', '=', 2],
            ])
                ->field('main_order_id,payment_amount,refund_money')
                ->select()->toArray();


            $refund_sub_orders = [];
            foreach ($sub_orders as $sub_order) {
                if (isset($refund_sub_orders[$sub_order['main_order_id']])) {
                    $refund_sub_orders[$sub_order['main_order_id']] = bcadd($refund_sub_orders[$sub_order['main_order_id']], $sub_order['payment_amount'], 2);
                } else {
                    $refund_sub_orders[$sub_order['main_order_id']] = $sub_order['payment_amount'];
                }
            }


            foreach ($orders as $order) {
                $data[$user[$admin_id]][] = [$order['uid'], $order['main_order_no'],
                    bcsub($order['payment_amount'], ($refund_sub_orders[$order['id']] ?? '0'), 2)
                    , date('Y-m-d H:i:s', $order['created_time']),
                    in_array($order['id'], $labels_main_ids) ? '分享' : '否'
                    , '沉默用户'];
            }


            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('20231117' . $user[$admin_id] . '.xlsx', 'sheet1');

            $filePath = $excel
                ->data($data[$user[$admin_id]])
                ->output();
            return $filePath;


        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }


    //首三单第三单
    public function ssd($param = [])
    {
        $file = 'ssd.xlsx';
        $arr  = $this->readExcel($file);


        $data = [];

        $sheet1 = $arr['sheet1']['list'];
        $data[] = array_shift($sheet1);
        $uids   = array_column($sheet1, '0');


        try {

            //查询有效用户的全部订单
            $origin_orders = Db::connect('database_orders_prod')->name('order_main')
                ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                ->where('uid', 'in', $uids)
                ->order('created_time', 'ASC')
                ->field('uid,main_order_no,payment_amount,created_time,id')
                ->select()->toArray();
            $order_group   = [];
            foreach ($origin_orders as $origin_order) {
                $order_group[$origin_order['uid']][] = $origin_order;
            }
            $thirdly_orders = array_column($order_group, '2');
            $thirdly_orders = array_column($thirdly_orders, null, 'uid');

            //子单查询
            $thirdly_orders_ids = array_column($thirdly_orders, 'id');
            $sub_orders         = Es::name(Es::ORDERS)->where([
                ['main_order_id', 'in', $thirdly_orders_ids],
            ])
                ->field('id,period,main_order_id,payment_amount,package_id,sub_order_status,order_type,coupon_id')
                ->select()->toArray();
            $package_ids        = array_column($sub_orders, 'package_id');
            $coupon_use_ids     = array_column($sub_orders, 'coupon_id');
            $sub_order_group    = [];
            foreach ($sub_orders as $sub_order) {
                $sub_order_group[$sub_order['main_order_id']][] = $sub_order;
            }

            $coupon_issue_list = Db::connect('mysql_marketing_prod')->name('coupon_issue')
                ->where('id', 'in', $coupon_use_ids)->column('coupon_id', 'id');
            $temp_packages     = Es::name(Es::PERIODS_PACKAGE)->where([
                ['id', 'in', $package_ids]
            ])->field('id,associated_products,price')->select()->toArray();

            $packages = [];
            $pids     = [];
            foreach ($temp_packages as $temp_package) {
                $associated_products           = json_decode($temp_package['associated_products'], true);
                $package_pids                  = array_column($associated_products, 'product_id');
                $packages[$temp_package['id']] = ['price' => $temp_package['price'], 'pids' => $package_pids];
                $pids                          = array_merge($pids, $package_pids);
            }
            $pids = array_values(array_unique($pids));

            $wiki_products = Es::name(Es::PRODUCTS)->where([
                ['id', 'in', $pids]
            ])
                ->field('id,cn_product_name,en_product_name,product_category_name,product_type_name')
                ->select()->toArray();
            $wiki_products = array_column($wiki_products, null, 'id');

            $sub_order_status = [
                0 => '待支付', 1 => '已支付', 2 => '已发货', 3 => '已完成', 4 => '已取消',
            ];
            $sub_order_type   = [
                0 => '闪购', 1 => '秒发', 2 => '跨境', 3 => '尾货', 4 => '兔头实物', 5 => '酒会(酒历)', 6 => '课程', 7 => '三方', 8 => '线下', 9 => '商家秒发', 10 => '商家闪购', 11 => '拍卖',
            ];
            foreach ($uids as $uid) {
                $main_order = $thirdly_orders[$uid] ?? null;
                if ($main_order) {
                    $sub_order_items = $sub_order_group[$main_order['id']];
                    if (empty($sub_order_items)) {
                        $data[] = [
                            $uid, $main_order['main_order_no'], date('Y-m-d H:i:s', $main_order['created_time']),
                            '', '', '', '',
                            $main_order['payment_amount'],
                            '',
                            '',
                            $coupon_issue_list[$sub_order_item['coupon_id'] ?? '0'] ?? ''
                        ]; // todo
                    } else {

                        foreach ($sub_order_items as $sub_order_item) {
                            $package_info = $packages[$sub_order_item['package_id']];
                            $item_pids    = $package_info['pids'];

                            if (empty($item_pids)) {
                                $data[] = [
                                    $uid, $main_order['main_order_no'], date('Y-m-d H:i:s', $main_order['created_time']),
                                    $sub_order_item['period'],

                                    '', '',
                                    $package_info['price'],
                                    $main_order['payment_amount'],
                                    ($sub_order_type[$sub_order_item['order_type']] ?? $sub_order_item['order_type']),
                                    ($sub_order_status[$sub_order_item['sub_order_status']] ?? $sub_order_item['sub_order_status']),
                                    $coupon_issue_list[$sub_order_item['coupon_id'] ?? '0'] ?? ''
                                ];
                            } else {
                                foreach ($item_pids as $item_pid) {
                                    $item_product = $wiki_products[$item_pid] ?? [];
                                    $data[]       = [
                                        $uid, $main_order['main_order_no'], date('Y-m-d H:i:s', $main_order['created_time']),
                                        $sub_order_item['period'],
                                        ($item_product['cn_product_name'] ?? $item_pid) . ' ' . ($item_product['en_product_name'] ?? ''),
                                        ($item_product['product_category_name'] ?? '') . ' ' . ($item_product['product_type_name'] ?? ''),
                                        $package_info['price'],
                                        $main_order['payment_amount'],
                                        ($sub_order_type[$sub_order_item['order_type']] ?? $sub_order_item['order_type']),
                                        ($sub_order_status[$sub_order_item['sub_order_status']] ?? $sub_order_item['sub_order_status']),
                                        $coupon_issue_list[$sub_order_item['coupon_id'] ?? '0'] ?? ''
                                    ];
                                }
                            }
                        }
                    }

                } else {
                    $data[] = [$uid, '', '', '', '', '', '', '', '', '', ''];
                }
            }

            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('20231108第三单.xlsx', 'sheet1');

            $filePath = $excel
                ->data($data)
                ->output();
            return $filePath;


        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }

    /**
     * @方法描述: 读取Excel
     * <AUTHOR>
     * @Date 2022/9/6 10:24
     * @param $filename
     * @return array
     */
    public function readExcel($filename)
    {
        $path = app()->getRuntimePath() . 'excel' . DIRECTORY_SEPARATOR;

//        $path   = $path.$filename;
//        $startI = 1;
//        #下载文件
//        $local_path = download_image($path, 'xls');
//        #解析文件
//        $data = getExcelData($local_path, $startI);
//
//        @unlink($local_path);
//        $excelData = $data['data'];
//        unset($excelData[$startI]);
//        return $excelData;

        $config    = ['path' => $path];
        $excel     = new \Vtiful\Kernel\Excel($config);
        $sheetList = $excel->openFile($filename)->sheetList();
        $data      = [
            'path'      => $path,
            'file_name' => $filename,
        ];

        $i = 1;
        foreach ($sheetList as $sheetName) {

            $sheetData = $excel
                ->openSheet($sheetName)
                ->getSheetData();

            $data['sheet' . $i] = [
                'name' => $sheetName,
                'list' => $sheetData,
            ];
            $i++;
        }
        return $data;
    }


    public function auctionTransfer($param = [])
    {
        $month      = '2023-12';
        $start_time = strtotime($month);
        $end_time   = strtotime($month . '+1month -1second');

        try {
            $data = [];

            $list = Db::connect('pord_mysql_auction')->name('transfer_log')->alias('l')
                ->join('user u', 'u.uid=l.uid')
                ->join('orders o', 'o.order_no=l.main_order_no')
                ->whereBetweenTime('l.created_time', $start_time, $end_time)
                ->field('l.name,u.id_card,u.telephone_encrypt,l.main_order_no,l.main_order_no,o.payment_time,o.payment_time,l.total_amount,l.service_charge,l.amount,l.individual_tax,l.amount as amount2 ,l.created_time')
                ->select()->toArray();

            $header = ['姓名', '身份证号', '电话号码', '订单号', '订单支付时间', '订单金额', '服务费', '应付客户金额', '代扣个人所得税', '支付金额', '支付客户时间'];

            $enc_data = array_values(array_unique(array_merge(array_column($list, 'telephone_encrypt'), array_column($list, 'name'), array_column($list, 'id_card'))));
            $dec_data = \Curl::cryptionDeal($enc_data);

            foreach ($list as $item) {
                $item['payment_time']      = date('Y-m-d H:i:s', $item['payment_time']);
                $item['telephone_encrypt'] = $dec_data[$item['telephone_encrypt']] ?? $item['telephone_encrypt'];
                $item['name']              = $dec_data[$item['name']] ?? $item['name'];
                $item['id_card']           = $dec_data[$item['id_card']] ?? $item['id_card'];
                $data[]                    = array_values($item);
            }


            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName($month . '个人拍品转账数据.xlsx', $month);

            $filePath = $excel
                ->header($header)
                ->data($data)
                ->output();

            return $filePath;
        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }

    public function shUser($param = [])
    {
        $citys = ['10' => '上海'];

        try {
            $data = [];

//            $silence_uids = Db::connect('mysql_marketing_prod')->name('special_activity_target_user')//沉默用户
//            ->where('special_activity_id', '999999')->column('vh_uid');  //沉默用户

            $export_data = [];

            foreach ($citys as $city_id => $city_name) {
                //1.指定城市用户
                $uids = Db::connect('pord_mysql_user')->name('user_address')
                    ->where('province_id', $city_id)
//                    ->where('city_id', $city_id)
                    ->where('is_delete', 0)
                    ->where('is_default', 1)
//                    ->where('uid', 'not in', $silence_uids)
//                        ->limit(0,100)
                    ->column('uid');

                $start_time = date('Y-m-d H:i:s', strtotime('-3 year')); //三年前
//                $consume_uids = Db::connect('database_orders_prod')->name('order_main')
//                    ->whereTime('created_time', '>=', strtotime($start_time)) //至今
//                    ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
//                    ->where('uid', 'in', $uids)
//                    ->group('uid')
//                    ->column('uid'); //3年消费过的用户
                $enc_data = Db::connect('pord_mysql_user')->name('user')
                    ->where('uid', 'in', $uids)
                    ->where('is_delete', 0)//未删除的用户
                    ->column('telephone');
                $enc_data = array_chunk($enc_data, 500);


                $enc_err_data = [];

                foreach ($enc_data as $enc_datum) {
                    $dec_data = \Curl::cryptionDeal($enc_datum);
                    if (empty($dec_data)) {
                        $enc_err_data[$city_name][] = $enc_datum;
                    } else {
                        foreach ($enc_datum as $enc_key) {
                            $export_data[$city_name][] = [$dec_data[$enc_key] ?? ''];
                        }
                    }
                }

                foreach ($enc_err_data as $enc_err_datum) {
                    $export_data[$city_name] = [json_encode($enc_err_datum)];
                }
            }

            $export_data = array_chunk($export_data['上海'], 7000);

            $path = app()->getRuntimePath() . 'excel';
            if (!is_dir($path)) {
                mkdir($path, 0755, true);
            }
            $excel = new \Vtiful\Kernel\Excel(compact('path'));
            $excel->fileName('上海默认收货地址用户手机号.xlsx', '上海1');

            $filePath = $excel
                ->data($export_data[0])
                ->addSheet('上海2')
                ->data($export_data[1])
                ->addSheet('上海3')
                ->data($export_data[2])
                ->addSheet('上海4')
                ->data($export_data[3])
                ->output();

            return $filePath;
        } catch (\Exception $e) {
            print_r($e->getMessage() . $e->getLine());
            die;
        }

    }


    /**
     * @方法描述: 尾货直播下单用户手机号;
     * <AUTHOR>
     * @Date 2023/11/21 16:58
     * @param $param
     * @return string
     */
    public function leftoverUser($param = [])
    {
        $out_excel_name = '尾货直播下单用户手机号';

        //1.购买过商品名称包含"直播特卖"的商品的用户
        $zbtm_periods = Es::name(Es::PERIODS)->where([
            ['title', 'like', '*直播特卖*']
        ])->field('id')
            ->order(['id' => 'desc'])
            ->select()->toArray();
        $zbtm_periods = array_column($zbtm_periods, 'id');

        $zbtm_orders     = Es::name(Es::ORDERS)->where([
            ['period', 'in', $zbtm_periods],
            ['sub_order_status', 'in', [1, 2, 3]],
        ])->field('uid')->order(['id' => 'desc'])->select()->toArray();
        $zbtm_order_uids = array_values(array_unique(array_column($zbtm_orders, 'uid')));

        $zbtm_phones = getSuccessPhone($zbtm_order_uids);

        //2.近一个月购买过尾货板块的用户
        $wh_orders     = Es::name(Es::ORDERS)->where([
            ['order_type', 'in', [3]], //订单类型：0-闪购 1-秒发 2-跨境 3-尾货 4-兔头实物 5-酒会(酒历) 6-课程 7-三方 8-线下
            ['sub_order_status', 'in', [1, 2, 3]],
            ['created_time', '>=', date("Y-m-d H:i:s", strtotime('-1 month'))],
        ])
            ->field('uid')
            ->order(['id' => 'desc'])->select()->toArray();
        $wh_order_uids = array_values(array_unique(array_column($wh_orders, 'uid')));
        $wh_phones     = getSuccessPhone($wh_order_uids);


        $out_excel_data = [
            '购买过名称含直播特卖用户'     => array_values($zbtm_phones),
            '近一个月购买过尾货板块的用户' => array_values($wh_phones),
        ];

        return outExcel($out_excel_data, $out_excel_name);
    }


    /**
     * @方法描述: 上海地区赠送ICON VINEHOO门票50用户
     * <AUTHOR>
     * @Date 2023/11/23 10:44
     * @param $param
     */
    public function shTop50User($param = [])
    {
        $out_excel_name = '上海地区赠送ICON VINEHOO门票50用户';

        $data[]     = ['ID', '手机号', '姓名', '呢称', '成交额',];
        $sh_city_id = 10; //上海城市ID

        //1.指定城市用户
        $uids = Db::connect('pord_mysql_user')->name('user_address')
            ->where('province_id', $sh_city_id)
            ->where('is_delete', 0)
            ->where('is_default', 1)
            ->column('uid');

        //查询上海用户两年内全部有效订单
        $orders = Es::name(Es::ORDERS)->where([
            ['uid', 'in', $uids],
            ['sub_order_status', 'in', [1, 2, 3]],
            ['created_time', '>=', date("Y-m-d H:i:s", strtotime('-2year'))],
        ])
            ->order(['created_time' => 'DESC'])
            ->field('id,uid,consignee,payment_amount,created_time,nickname')
            ->select()
            ->toArray();

        $order_group = [];
        foreach ($orders as $order) {
            $order_group[$order['uid']][] = $order;
        }

        $top_user_orders = [];
        foreach ($order_group as $uid => $og) {
            $top_user_orders[] = [
                'uid'            => $uid,
                'name'           => $og[0]['consignee'],
                'nickname'       => implode(array_unique(array_column($og, 'nickname'))),
                'payment_amount' => round(array_sum(array_column($og, 'payment_amount')), 2),
            ];
        }

        $top_sort = array_column($top_user_orders, 'payment_amount');
        array_multisort($top_sort, SORT_DESC, $top_user_orders);
        $top50_user_orders = array_slice($top_user_orders, 0, 50);

        $user_phone = getSuccessPhone(array_column($top50_user_orders, 'uid'));
        $dec_data   = \Curl::cryptionDeal(array_column($top50_user_orders, 'name'));

        foreach ($top50_user_orders as $top_user_info) {
            $data[] = [$top_user_info['uid'], $user_phone[$top_user_info['uid']], $dec_data[$top_user_info['name']], $top_user_info['nickname'], $top_user_info['payment_amount'],];
        }

        $out_excel_data = [
            '海地区赠送ICON VINEHOO门票用户' => $data,
        ];

        return outExcel($out_excel_data, $out_excel_name);
    }


    /**
     * @方法描述: 售前客服绩效202311数据导出
     * <AUTHOR>
     * @Date 2023/12/4 18:31
     * @param $param
     */
    public function jxUser202311($param = [])
    {
        $user  = [
            '315' => '肖恩静',
            '381' => '周松',
//            '210' => '梅林丽',
            '408' => '金爱梅',
        ];
        $start = "2023-12-01 00:00:00";
        $end   = "2023-12-28 23:59:59";

        $data = [];

        foreach ($user as $admin_id => $admin_name) {
            $data[$admin_name][] = ['UID', '子订单号', '子订单金额'];

            $u_arr = Db::connect('pord_mysql_user')->name('user_portrait_set')
                ->where('admin_id', $admin_id)
                ->whereBetweenTime('claim_time', $start, $end)
                ->column('claim_time','uid');

            $uids = array_keys($u_arr);


            try {

                //1. 只保留11月注册的用户
                $uids = Db::connect('pord_mysql_user')->name('user')
                    ->where('uid', 'in', $uids)
                    ->whereBetweenTime('created_time', $start, $end)
                    ->column('uid');

                //过滤新用户  todo 过滤拍卖..
                foreach ($uids as $item_uid) {
                    $u_orders = Db::connect('database_orders_prod')->name('order_main')
                        ->whereTime('created_time', '>=', date('Y-m-d H:i:s', $u_arr[$item_uid]))
                        ->where('main_order_status', 'in', [1, 2, 3]) //主订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                        ->where('uid', '=', $item_uid)
                        ->order('created_time', 'ASC')
                        ->select()->toArray();
                    if (!empty($u_orders)) {

                        $main_order_ids = array_column($u_orders, 'id');

                        //查询子单退款金额
                        $sub_orders = Es::name(Es::ORDERS)->where([
                            ['main_order_id', 'in', $main_order_ids],
                            ['sub_order_status', 'in', [1, 2, 3]],
                            ['refund_status', '<>', 2],
                        ])
                            ->order(['created_time' => 'ASC'])
                            ->field('main_order_id,sub_order_no,uid,payment_amount,created_time')
                            ->limit(0, 3)
                            ->select()->toArray();

                        $sub_group = [];
                        foreach ($sub_orders as $sub_order) {
                            $data[$admin_name][] = [$sub_order['uid'], $sub_order['sub_order_no'], $sub_order['payment_amount'], $sub_order['created_time'],];
//                            $sub_group[$sub_order['main_order_id']][] = $sub_order;
                        }

//                        $i = 1;
//                        foreach ($sub_group as $sub_g) {
//                            if ($i <= 3) {
//                                foreach ($sub_g as $s_g) {
//                                    $data[$admin_name][] = [$s_g['main_order_id'], $s_g['uid'], $s_g['sub_order_no'], $s_g['payment_amount'],];
//                                }
//                            }
//                            $i++;
//                        }
                    }
                }

            } catch (\Exception $e) {
                print_r($e->getMessage() . $e->getLine());
                die;
            }
        }


        print_r(outExcel($data, '绩效导出'));
        die;


    }





















    #################################正式服接口#########################

    //开票
    public function createD(Request $request)
    {
        $url   = "https://callback.vinehoo.com/go-invoice/go-invoice/v3/create/createD";
        $param = $request->post();

        $order_nos   = [];
        $sub_orders  = [];
        $order_total = 0;
        foreach ($param['orders'] as $order) {
            $sub_order   = Es::name(Es::ORDERS)->where([
                ['sub_order_no', '=', $order['order_no']],
            ])->find();
            $order_nos[] = $order['order_no'];

            $order_t = 0;

//            print_r($sub_orders);die;

            foreach ($order['products'] as $product) {
                $product['price'] = strval($product['price']);
                $product['num']   = strval($product['num']);

                $product_total_price = $product['price'] * $product['num'];
                $order_total         = $product_total_price + $order_total;
                $order_t             = $product_total_price + $order_t;
                print_r("{$order['order_no']} {$product['code']}  {$product['price']} * {$product['num']} = $product_total_price  ---  {$sub_order['payment_amount']}" . PHP_EOL);

            }

            print_r(" {$sub_order['payment_amount']} $order_t " . ($sub_order['payment_amount'] == $order_t ? 'success' : 'ERROR') . PHP_EOL . PHP_EOL);

        }


//        print_r($sub_orders);die;
//
//        print_r($order_nos);die;


        print_r(PHP_EOL);
        print_r($order_total);
        print_r(PHP_EOL);
        var_dump($param);
        die;

        die;


    }


}