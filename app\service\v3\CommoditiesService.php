<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Commodities;
use app\Request;
use app\service\es\Es;
use app\validate\CommoditiesValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 商品
 * Class CommoditiesService
 * @package app\service\v3
 */
class CommoditiesService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Commodities;
        $this->validate    = CommoditiesValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/05/23 13:45
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    public function syncSecondData($param)
    {
        $db = 'mysql_commodities'; //mysql_commodities_prod
//        ES
        //RECOMMEND_URL_DEV
        //$is_hidden
        //is_channel

        $error_ids = [];
        $items     = Db::connect($db)->name('periods_second')
            ->where('onsale_time', '>', '0')
            ->where('onsale_status', '=', '2')
            ->where('is_channel', '=', 0) //非渠道商品
            ->column('id,title,onsale_status,price,label,country,product_category,onsale_time');

        $all_recommend_labels = Db::connect($db)->name('recommend_label')
            ->where([
                ['type', '=', 2],
                ['is_delete', '=', 0],
            ])->column('name', 'id'); //商品标签

        foreach ($items as $item) {
            try {
                $is_hidden = in_array($item['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
//                $is_hidden = 1;//int64	是否隐藏（0：不隐藏，1：隐藏）


                //region 查询产品
                $product_ids = [];
                $packages    = Es::name('periods_set')->where([
                    ['period_id', '=', $item['id']],
                    ['is_hidden', '=', '0'],
                ])->field('associated_products')->select()->toArray();

                foreach ($packages as $package) {
                    $associated_products = json_decode($package['associated_products'], true);
                    $product_ids         = array_merge($product_ids, array_column($associated_products, 'product_id'));
                }

                $product_list = Es::name('panshi.products')
                    ->where([
                        ['id', 'in', array_values(array_unique($product_ids))],
                    ])
                    ->field('grape,chateau_name_cn,regions_name_cn')
                    ->select()->toArray();


                foreach ($product_list as $k => $var) {
                    $product_list[$k] = implode(',', array_values($var));
                }
                $product_label_string = $this->obliqueLineToMinus(implode(',', array_values($product_list)));


                $recommend_labels = [];
                if ($item['label']) {
                    foreach (explode(',', $item['label']) as $label_id) {
                        $recommend_labels[] = $all_recommend_labels[$label_id] ?? '';
                    }
                }

                if (($item['price'] > 0) && ($item['price'] <= 200)) {
                    $recommend_labels[] = '0至200';
                } elseif (($item['price'] > 0) && ($item['price'] <= 500)) {
                    $recommend_labels[] = '200至500';
                } elseif ($item['price'] > 500) {
                    $recommend_labels[] = '500以上';
                }

                $labels = array_unique($this->flatten([
                    explode(',', $this->obliqueLineToMinus($item['country'])), //国家
                    explode(',', $this->obliqueLineToMinus($item['product_category'])), //类别
                    explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                    explode(',', $product_label_string), //标签
                ]));
                //endregion 查询产品

                $push_data = [
                    'item_id'     => "second-{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                    'categories'  => ["秒发"], //[]string
                    'comment'     => $this->obliqueLineToMinus($item['title']), //string
                    'is_hidden'   => $is_hidden, //int64	是否隐藏（0：不隐藏，1：隐藏）
                    'labels'      => array_values(array_filter($labels)), //[]string	标签(项目个性化标签,例如:小甜水)
                    'create_time' => intval($item['onsale_time']), //	int64	上新时间(时间戳)
                ];

                $url        = env('ITEM.RECOMMEND_URL_DEV') . '/go-recommend/v3/item/modify';
                $code       = httpCurl($url, 'post', json_encode($push_data));
                $userResult = json_decode($code, true);

                if ($userResult['error_code'] != '0') {
                    throw new Exception("syncSale 同步数据失败: request-" . json_encode($push_data) . ' ret_data-' . $code);
                }
            } catch (\Exception $e) {
                print_r($e->getMessage());
                die;
                $error_ids[] = $item['id'];
                Log::error($item['id'] . ' ' . $e->getMessage());
            }

        }

        return json_encode($error_ids);

        print_r($error_ids);
        die;
    }

    public function syncSecondDataProd($param)
    {
        $db = 'mysql_commodities_prod'; //
//        ES
        //RECOMMEND_URL_DEV
        //$is_hidden
        //is_channel

        $error_ids = [];
        $items     = Db::connect($db)->name('periods_second')
            ->where('onsale_time', '>', '0')
//            ->where('onsale_status', '=', '2')
            ->where('id', '=', '122656') // todo
            ->where('is_channel', '=', 0) //非渠道商品
            ->column('id,title,onsale_status,price,label,country,product_category,onsale_time');

        $all_recommend_labels = Db::connect($db)->name('recommend_label')
            ->where([
                ['type', '=', 2],
                ['is_delete', '=', 0],
            ])->column('name', 'id'); //商品标签


        foreach ($items as $item) {
            try {
//                $is_hidden = in_array($item['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                $is_hidden = 1;//int64	是否隐藏（0：不隐藏，1：隐藏）


                //region 查询产品
                $product_ids = [];
                $packages    = Es::name('periods_set')->where([
                    ['period_id', '=', $item['id']],
                    ['is_hidden', '=', '0'],
                ])->field('associated_products')->select()->toArray();

                foreach ($packages as $package) {
                    $associated_products = json_decode($package['associated_products'], true);
                    $product_ids         = array_merge($product_ids, array_column($associated_products, 'product_id'));
                }

                $product_list = Es::name('panshi.products')
                    ->where([
                        ['id', 'in', array_values(array_unique($product_ids))],
                    ])
                    ->field('grape,chateau_name_cn,regions_name_cn')
                    ->select()->toArray();


                foreach ($product_list as $k => $var) {
                    $product_list[$k] = implode(',', array_values($var));
                }
                $product_label_string = $this->obliqueLineToMinus(implode(',', array_values($product_list)));


                $recommend_labels = [];
                if ($item['label']) {
                    foreach (explode(',', $item['label']) as $label_id) {
                        $recommend_labels[] = $all_recommend_labels[$label_id] ?? '';
                    }
                }

                if (($item['price'] > 0) && ($item['price'] <= 200)) {
                    $recommend_labels[] = '0至200';
                } elseif (($item['price'] > 0) && ($item['price'] <= 500)) {
                    $recommend_labels[] = '200至500';
                } elseif ($item['price'] > 500) {
                    $recommend_labels[] = '500以上';
                }

                $labels = array_unique($this->flatten([
                    explode(',', $this->obliqueLineToMinus($item['country'])), //国家
                    explode(',', $this->obliqueLineToMinus($item['product_category'])), //类别
                    explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                    explode(',', $product_label_string), //标签
                ]));
                //endregion 查询产品

                $push_data = [
                    'item_id'     => "second-{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                    'categories'  => ["秒发"], //[]string
                    'comment'     => $this->obliqueLineToMinus($item['title']), //string
                    'is_hidden'   => $is_hidden, //int64	是否隐藏（0：不隐藏，1：隐藏）
                    'labels'      => array_values(array_filter($labels)), //[]string	标签(项目个性化标签,例如:小甜水)
                    'create_time' => intval($item['onsale_time']), //	int64	上新时间(时间戳)
                ];

                $url        = env('ITEM.RECOMMEND_URL_PROD') . '/go-recommend/v3/item/modify';
                $code       = httpCurl($url, 'post', json_encode($push_data));
                $userResult = json_decode($code, true);

                if ($userResult['error_code'] != '0') {
                    throw new Exception("syncSale 同步数据失败: request-" . json_encode($push_data) . ' ret_data-' . $code);
                }
            } catch (\Exception $e) {
                print_r($e->getMessage());
                die;
                $error_ids[] = $item['id'];
                Log::error($item['id'] . ' ' . $e->getMessage());
            }

        }

        return json_encode($error_ids);

        print_r($error_ids);
        die;
    }


    //斜线转成-号
    public function obliqueLineToMinus($string)
    {
        $arr = [
            '/'  => '-',
            '\\' => '-',
        ];

        $string = str_replace(array_keys($arr), array_values($arr), $string);

        return $string;
    }

    //多维转一维数组
    public function flatten($arr)
    {
        $result = [];
        foreach ($arr as $value) {
            if (is_array($value)) {
                $result = array_merge($result, $this->flatten($value));
            } else {
                $result[] = $value;
            }
        }
        return $result;
    }


    //闪购商品同步
    public function syncFlashData($param)
    {
        $db = 'mysql_commodities'; //mysql_commodities_prod
//        ES
        //RECOMMEND_URL_DEV  todo
        //$is_hidden
        //is_channel

        $error_ids = [];
        $items     = Db::connect($db)->name('periods_flash')
            ->where('onsale_time', '>', 0)
            ->where('onsale_status', '=', '2')
            ->column('id,title,is_channel,onsale_status,price,label,country,product_category,onsale_time');

        $all_recommend_labels = Db::connect($db)->name('recommend_label')
            ->where([
                ['type', '=', 2],
                ['is_delete', '=', 0],
            ])->column('name', 'id'); //商品标签

        foreach ($items as $item) {
            try {
                if ($item['is_channel']) {
                    $is_hidden = 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                } else {
                    $is_hidden = in_array($item['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                }

                //region 查询产品
                $product_ids = [];
                $packages    = Es::name('periods_set')->where([
                    ['period_id', '=', $item['id']],
                    ['is_hidden', '=', '0'],
                ])->field('associated_products')->select()->toArray();

                foreach ($packages as $package) {
                    $associated_products = json_decode($package['associated_products'], true);
                    $product_ids         = array_merge($product_ids, array_column($associated_products, 'product_id'));
                }

                $product_list = Es::name('panshi.products')
                    ->where([
                        ['id', 'in', array_values(array_unique($product_ids))],
                    ])
                    ->field('grape,chateau_name_cn,regions_name_cn,product_category')
                    ->select()->toArray();

                $is_wine = true;
                if (empty($product_list)) {
                    $is_wine = false;
                }

                foreach ($product_list as $k => $var) {
                    if (!in_array($var['product_category'], [1])) {
                        $is_wine = false;
                    }
                    unset($var['product_category']);
                    $product_list[$k] = implode(',', array_values($var));
                }

                if (!$is_wine) {
                    throw new Exception("包含非酒类产品!");
                }

                $product_label_string = $this->obliqueLineToMinus(implode(',', array_values($product_list)));


                $recommend_labels = [];
                if ($item['label']) {
                    foreach (explode(',', $item['label']) as $label_id) {
                        $recommend_labels[] = $all_recommend_labels[$label_id] ?? '';
                    }
                }

                if (($item['price'] > 0) && ($item['price'] <= 200)) {
                    $recommend_labels[] = '0至200';
                } elseif (($item['price'] > 0) && ($item['price'] <= 500)) {
                    $recommend_labels[] = '200至500';
                } elseif ($item['price'] > 500) {
                    $recommend_labels[] = '500以上';
                }

                $labels = array_unique($this->flatten([
                    explode(',', $this->obliqueLineToMinus($item['country'])), //国家
                    explode(',', $this->obliqueLineToMinus($item['product_category'])), //类别
                    explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                    explode(',', $product_label_string), //标签
                ]));
                //endregion 查询产品

                $push_data = [
                    'item_id'     => "{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                    'categories'  => ["闪购"], //[]string
                    'comment'     => $this->obliqueLineToMinus($item['title']), //string
                    'is_hidden'   => $is_hidden, //int64	是否隐藏（0：不隐藏，1：隐藏）
                    'labels'      => array_values(array_filter($labels)), //[]string	标签(项目个性化标签,例如:小甜水)
                    'create_time' => intval($item['onsale_time']), //	int64	上新时间(时间戳)
                ];


                $url        = env('ITEM.RECOMMEND_URL_DEV') . '/go-recommend/v3/item/activityModify';
                $code       = httpCurl($url, 'post', json_encode($push_data));
                $userResult = json_decode($code, true);

                if ($userResult['error_code'] != '0') {
                    throw new Exception("syncSale 同步数据失败: request-" . json_encode($push_data) . ' ret_data-' . $code);
                }
            } catch (\Exception $e) {
                if ($e->getMessage() != '包含非酒类产品!') {
                    $error_ids[] = $item['id'];
                    Log::error($item['id'] . ' ' . $e->getMessage());
                }
            }

        }

        return json_encode($error_ids);

        print_r($error_ids);
        die;
    }


    public function syncFlashDataProd($param)
    {
        $db        = 'mysql_commodities_prod'; //mysql_commodities_prod
        $error_ids = [];


        $pages = range(1, 76);
        $limit = 1000;


        foreach ($pages as $page) {
            $pagestart = ($page - 1) * $limit;

            $items = Db::connect($db)->name('periods_flash')
                ->where('onsale_time', '>', 0)
                ->where('onsale_status', '=', '2')
                ->limit($pagestart, $limit)
                ->column('id,title,is_channel,onsale_status,price,label,country,product_category,onsale_time');

            $all_recommend_labels = Db::connect($db)->name('recommend_label')
                ->where([
                    ['type', '=', 2],
                    ['is_delete', '=', 0],
                ])->column('name', 'id'); //商品标签

            foreach ($items as $item) {
                try {
                    if ($item['is_channel']) {
                        $is_hidden = 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                    } else {
                        $is_hidden = in_array($item['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                    }

                    //region 查询产品
                    $product_ids = [];
                    $packages    = Es::name('periods_set')->where([
                        ['period_id', '=', $item['id']],
                        ['is_hidden', '=', '0'],
                    ])->field('associated_products')->select()->toArray();

                    foreach ($packages as $package) {
                        $associated_products = json_decode($package['associated_products'], true);
                        $product_ids         = array_merge($product_ids, array_column($associated_products, 'product_id'));
                    }


                    $product_list = Es::name('panshi.products')
                        ->where([
                            ['id', 'in', array_values(array_unique($product_ids))],
                        ])
                        ->field('grape,chateau_name_cn,regions_name_cn,product_category')
                        ->select()->toArray();

                    $is_wine = true;
                    if (empty($product_list)) {
                        $is_wine = false;
                    }

                    foreach ($product_list as $k => $var) {
                        if (!in_array($var['product_category'], [1])) {
                            $is_wine = false;
                        }
                        unset($var['product_category']);
                        $product_list[$k] = implode(',', array_values($var));
                    }

                    if (!$is_wine) {
                        throw new Exception("包含非酒类产品!");
                    }

                    $product_label_string = $this->obliqueLineToMinus(implode(',', array_values($product_list)));


                    $recommend_labels = [];
                    if ($item['label']) {
                        foreach (explode(',', $item['label']) as $label_id) {
                            $recommend_labels[] = $all_recommend_labels[$label_id] ?? '';
                        }
                    }

                    if (($item['price'] > 0) && ($item['price'] <= 200)) {
                        $recommend_labels[] = '0至200';
                    } elseif (($item['price'] > 0) && ($item['price'] <= 500)) {
                        $recommend_labels[] = '200至500';
                    } elseif ($item['price'] > 500) {
                        $recommend_labels[] = '500以上';
                    }

                    $labels = array_unique($this->flatten([
                        explode(',', $this->obliqueLineToMinus($item['country'])), //国家
                        explode(',', $this->obliqueLineToMinus($item['product_category'])), //类别
                        explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                        explode(',', $product_label_string), //标签
                    ]));
                    //endregion 查询产品

                    $push_data = [
                        'item_id'     => "{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                        'categories'  => ["闪购"], //[]string
                        'comment'     => $this->obliqueLineToMinus($item['title']), //string
                        'is_hidden'   => $is_hidden, //int64	是否隐藏（0：不隐藏，1：隐藏）
                        'labels'      => array_values(array_filter($labels)), //[]string	标签(项目个性化标签,例如:小甜水)
                        'create_time' => intval($item['onsale_time']), //	int64	上新时间(时间戳)
                    ];


                    $url        = env('ITEM.RECOMMEND_URL_DEV') . '/go-recommend/v3/item/activityModify';
                    $code       = httpCurl($url, 'post', json_encode($push_data));
                    $userResult = json_decode($code, true);

                    if ($userResult['error_code'] != '0') {
                        throw new Exception("syncSale 同步数据失败: request-" . json_encode($push_data) . ' ret_data-' . $code);
                    }
                } catch (\Exception $e) {
                    if ($e->getMessage() != '包含非酒类产品!') {
                        $error_ids[] = $item['id'];
                        Log::error($item['id'] . ' ' . $e->getMessage());
                    }
                }

            }

            sleep(1);
        }


        return json_encode($error_ids);

        print_r($error_ids);
        die;
    }


    public function syncFlashDataProdBath($param)
    {
        $db        = 'mysql_commodities_prod'; //mysql_commodities_prod
        $error_ids = [];

        $pages = range(1, 76);
        $limit = 1000;

        $push_datas = [];

        $all_recommend_labels = Db::connect($db)->name('recommend_label')
            ->where([
                ['type', '=', 2],
                ['is_delete', '=', 0],
            ])->column('name', 'id'); //商品标签

        try {


            foreach ($pages as $page) {

                print_r('$page ' . $page . ' ' . PHP_EOL);

                $pagestart = ($page - 1) * $limit;

                $items = Db::connect($db)->name('periods_flash')
                    ->where('onsale_time', '>', 0)
                    ->limit($pagestart, $limit)
                    ->column('id,title,is_channel,onsale_status,price,label,country,product_category,onsale_time');

                $period_ids = array_column($items, 'id');

                $packages = Es::name('periods_set')->where([
                    ['period_id', 'in', $period_ids],
                    ['is_hidden', '=', '0'],
                ])->field('id,period_id,associated_products')->select()->toArray();


                $product_ids = [];

                foreach ($packages as &$package1) {
                    $package2            = $package1;
                    $associated_products = json_decode($package2['associated_products'], true);
                    unset($package2['associated_products']);
                    if ($associated_products === null) $associated_products = [];
                    foreach ($associated_products as $p_item) {
                        if (!empty($p_item['product_id'])) {
                            if (is_array($p_item['product_id'])) {
                                foreach ($p_item['product_id'] as $p_item_p_id) {
                                    $package2['product_ids'][] = $p_item_p_id;
                                    $product_ids[]             = $p_item_p_id;
                                }
                            } else {
                                $package2['product_ids'][] = $p_item['product_id'];
                                $product_ids[]             = $p_item['product_id'];
                            }
                        }
                    }
                    $package1 = $package2;
                }

                $product_list = Es::name('panshi.products')
                    ->where([
                        ['id', 'in', array_values(array_unique($product_ids))],
                    ])
                    ->field('id,grape,chateau_name_cn,regions_name_cn,product_category')
                    ->select()->toArray();
                $product_list = array_column($product_list, null, 'id');


                $periods_product_labels = [];
                foreach ($packages as $package) {
                    if (!empty($package['product_ids'])) {
                        foreach ($package['product_ids'] as $package_product_id) {
                            empty($periods_product_labels[$package['period_id']]['labels']) && $periods_product_labels[$package['period_id']]['labels'] = [];

                            $package_product = $product_list[$package_product_id] ?? [];
                            if (!empty($package_product)) { //酒类产品
                                if ($package_product['product_category'] == '1') {
                                    !empty($package_product['chateau_name_cn']) && $periods_product_labels[$package['period_id']]['labels'][] = $package_product['chateau_name_cn'];
                                    !empty($package_product['grape']) && $periods_product_labels[$package['period_id']]['labels'][] = $package_product['grape'];
                                    !empty($package_product['regions_name_cn']) && $periods_product_labels[$package['period_id']]['labels'][] = $package_product['regions_name_cn'];
                                }
                            }
                        }
                    }
                }


                foreach ($items as $item) {
                    if ($item['is_channel']) {
                        $is_hidden = 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                    } else {
                        $is_hidden = in_array($item['onsale_status'], [1, 2]) ? 0 : 1;//int64	是否隐藏（0：不隐藏，1：隐藏）
                    }

                    //region 查询产品
                    $is_wine = true;


                    $period_labels = $periods_product_labels[$item['id']] ?? [];
                    if (empty($period_labels)) {
                        $is_wine = false;
                    }


                    if ($is_wine) {
                        $product_label_string = $this->obliqueLineToMinus(implode(',', $period_labels['labels']));

                        $recommend_labels = [];
                        if ($item['label']) {
                            foreach (explode(',', $item['label']) as $label_id) {
                                $recommend_labels[] = $all_recommend_labels[$label_id] ?? '';
                            }
                        }

                        if (($item['price'] > 0) && ($item['price'] <= 200)) {
                            $recommend_labels[] = '0至200';
                        } elseif (($item['price'] > 0) && ($item['price'] <= 500)) {
                            $recommend_labels[] = '200至500';
                        } elseif ($item['price'] > 500) {
                            $recommend_labels[] = '500以上';
                        }

                        $labels = array_unique($this->flatten([
                            explode(',', $this->obliqueLineToMinus($item['country'])), //国家
                            explode(',', $this->obliqueLineToMinus($item['product_category'])), //类别
                            explode(',', $this->obliqueLineToMinus(implode(',', $recommend_labels))), //标签
                            explode(',', $product_label_string), //标签
                        ]));
                        //endregion 查询产品


//                    $push_data = [
//                        'item_id'     => "{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
//                        'is_hidden'   => $is_hidden ? 'TRUE' : 'FALSE', //int64	是否隐藏（0：不隐藏，1：隐藏）
//                        'categories'  => '闪购', //[]string
//                        'time_stamp'  => date('Y-m-d H:i:s', $item['onsale_time']) . ' +0000 UTC', //	int64	上新时间(时间戳)
//                        'labels'      => implode('|', array_values(array_filter($labels))), //[]string	标签(项目个性化标签,例如:小甜水)
//                        'description' => $this->obliqueLineToMinus($item['title']), //string
//                    ];

                        $push_datas[] = [
                            "{$item['id']}", // string 项目id(全局唯一,使用来源加减号加数据id,例如秒发商品12:)
                            $is_hidden ? 1 : 0, //int64	是否隐藏（0：不隐藏，1：隐藏）
                            '["闪购"]', //[]string
                            date('Y-m-d H:i:s', $item['onsale_time']) . ' +0000 UTC', //	int64	上新时间(时间戳)
                            json_encode(array_values(array_filter($labels))), //[]string	标签(项目个性化标签,例如:小甜水)
                            $this->obliqueLineToMinus($item['title']), //string
                        ];


                    }


                }

                sleep(1);
            }


        } catch (\Exception $exception) {
            print_r('$exception->getMessage()');
            print_r($exception->getMessage());
            print_r($exception->getLine());
        }

        $header = ['item_id', 'is_hidden', 'categories', 'time_stamp', 'labels', 'description'];

        return $this->exportExcel([
            'filename'   => '闪购推荐数据完整.xlsx',
            'sheet_name' => '闪购推荐数据',
            'header'     => $header,
            'data'       => $push_datas,
        ]);

    }


    //通过采购人：刘歆韵 彭媛媛 为筛选条件，导出闪购全部期数及相关信息，具体需求见Excel表格。
    public function exportFlashPeriods($param)
    {
        $db = 'mysql_commodities_prod';

        try {

//CREATE TABLE `vh_periods_flash` (
//  `id` int(10) NOT NULL COMMENT '主键id',
//  `title` varchar(350) CHARACTER SET utf8mb4 NOT NULL COMMENT '闪购期数标题（名称）',
//  `brief` varchar(350) NOT NULL COMMENT '一句话介绍（副标题）',
//  `banner_img` varchar(350) NOT NULL COMMENT '题图',
//  `product_img` text NOT NULL COMMENT '产品图',
//  `video` varchar(350) DEFAULT NULL COMMENT '视频',
//  `detail` text NOT NULL COMMENT '题图详情',
//  `creator_name` varchar(10) NOT NULL COMMENT '文案添加人',
//  `creator_id` int(10) NOT NULL DEFAULT '0' COMMENT '文案添加人id',
//  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序字段',
//  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '售价',
//  `market_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '市场价',
//  `is_hidden_price` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏价格（0：显示，1：隐藏）',
//  `is_channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '渠道销售（0：否，1：是）',
//  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0：否，1：是）',
//  `is_presell` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否预售（0：否，1：是）',
//  `is_supplier_delivery` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否代发（0：否，1：是）',
//  `supplier_delivery_address` varchar(255) DEFAULT NULL COMMENT '代发（发货地）',
//  `is_parcel_insurance` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否保价（0：否，1：是）',
//  `is_support_reduction` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否支持满减（0：否，1：是）',
//  `is_support_coupon` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持优惠券（0：否，1：是）',
//  `is_support_ts` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持暂存（0：否，1：是）',
//  `sellout_sold_out` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否售完下架（0：否，1：是）',
//  `is_sold_out_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否下架锁定（0：否，1：是）',
//  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否爆款（0：否，1：是）',
//  `is_cold_chain` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否冷链（0：否，1：是）',
//  `predict_shipment_time` int(10) NOT NULL DEFAULT '0' COMMENT '预计发货时间',
//  `onsale_time` int(10) NOT NULL DEFAULT '0' COMMENT '上架时间',
//  `sell_time` int(10) NOT NULL DEFAULT '0' COMMENT '开售时间',
//  `sold_out_time` int(10) NOT NULL DEFAULT '0' COMMENT '下架时间',
//  `onsale_status` tinyint(1) DEFAULT '0' COMMENT '上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）',
//  `copywriting_review_status` tinyint(1) DEFAULT '0' COMMENT '文案审核状态（0：待提交，1：待审核，2：已通过，3：已驳回）',
//  `buyer_review_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '采购审核状态（0：待提交，1：已提交，2：待审核，3：已通过，4：已驳回）',
//  `onsale_review_status` tinyint(1) DEFAULT '0' COMMENT '运营审核状态（0：待绑定，1：待审核，2：审批中，3：已审核，4：已驳回）',
//  `onsale_review_time` int(10) DEFAULT '0' COMMENT '运营审核时间',
//  `onsale_verify_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '运营二次确认上架（0：未确认，1：已确认）',
//  `import_type` tinyint(1) DEFAULT '1' COMMENT '进口类型（0：自采，1：地采，2：跨境）',
//  `operation_id` int(10) DEFAULT '0' COMMENT '商品编辑运营者id',
//  `operation_name` varchar(10) DEFAULT NULL COMMENT '商品编辑运营者名称',
//  `operation_review_id` int(10) DEFAULT '0' COMMENT '商品运营审核者id',
//  `operation_review_name` varchar(10) DEFAULT NULL COMMENT '商品运营审核者名称',
//  `purchased` int(5) unsigned NOT NULL DEFAULT '0' COMMENT '真实已购',
//  `vest_purchased` int(5) DEFAULT '0' COMMENT '背心(马甲)已购',
//  `limit_number` int(5) unsigned DEFAULT '0' COMMENT '初始值',
//  `invariant_number` int(5) DEFAULT '0' COMMENT '不变初始值',
//  `critical_value` tinyint(2) DEFAULT '0' COMMENT '临界值',
//  `incremental` tinyint(2) DEFAULT '0' COMMENT '增量值',
//  `supplier` varchar(500) DEFAULT NULL COMMENT '供应商',
//  `supplier_id` int(10) DEFAULT NULL COMMENT '供应商id',
//  `buyer_id` int(10) DEFAULT '0' COMMENT '采购人id',
//  `product_id` varchar(100) DEFAULT '0' COMMENT '产品id（英文逗号分割：,）',
//  `created_time` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
//  `update_time` int(10) DEFAULT '0' COMMENT '更新时间',
//  `order_count` int(5) NOT NULL DEFAULT '0' COMMENT '已购订单数量',
//  `version` int(5) NOT NULL DEFAULT '0' COMMENT '当前版本号（更新叠加）',
//  `buyer_name` varchar(15) DEFAULT NULL COMMENT '采购人名称',
//  `quota_rule` text COMMENT '限购规则:{"quota_type": 0, // 限购类型（0：永久，1：每次\r\n"quota_number": 100, // 限购数量 "district": "重庆,成都", // 限购地区\r\n"rank": "1,10", // 限购等级"register_time": "2021-12-01" // 限购注册时间}',
//  `product_category` varchar(255) DEFAULT NULL COMMENT '商品类别',
//  `product_main_category` varchar(255) DEFAULT NULL COMMENT '商品主类',
//  `country` varchar(200) DEFAULT NULL COMMENT '国家',
//  `capacity` varchar(100) DEFAULT NULL COMMENT '容量',
//  `praise_count` int(10) NOT NULL DEFAULT '0' COMMENT '点赞总数',
//  `marketing_attribute` varchar(50) DEFAULT '0' COMMENT '营销属性（0：普通商品，1：拼团商品，2：新人商品）逗号分割',
//  `is_user_filter` tinyint(1) DEFAULT '0' COMMENT '是否用户过滤（0：否，1：是）',
//  `instruction` varchar(255) DEFAULT NULL COMMENT '加指令字段',
//  `uninstruction` varchar(255) DEFAULT NULL COMMENT '减指令字段',
//  `pageviews` int(10) DEFAULT '0' COMMENT '浏览量',
//  `video_cover` varchar(350) DEFAULT NULL COMMENT '视频封面',
//  `express_id` int(10) DEFAULT '1' COMMENT '快递模板id',
//  `short_code` varchar(255) DEFAULT NULL COMMENT '产品简码',
//  `product_keyword` varchar(255) DEFAULT NULL COMMENT '产品关键词',
//  `is_cold_free_shipping` tinyint(1) DEFAULT '0' COMMENT '是否冷链包邮（0：否，1：是）',
//  `is_postpone` tinyint(1) DEFAULT '0' COMMENT '是否自动延期（0：否，1：是）',
//  `payee_merchant_id` int(10) DEFAULT '1' COMMENT '收款商户id',
//  `payee_merchant_name` varchar(255) DEFAULT '重庆云酒佰酿电子商务有限公司' COMMENT '收款商户名称',
//  `ts_template` int(10) DEFAULT NULL COMMENT '暂存模板id',
//  `is_fail` tinyint(1) DEFAULT '1' COMMENT '是否有效（0：否，1：是）',
//  `label` varchar(255) DEFAULT NULL COMMENT '商品标签',
//  `is_ap` tinyint(3) DEFAULT '0' COMMENT '是否加购（0：否，1：是）',
//  `is_deposit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否订金：0-否 1-是',
//  `is_deposit_period` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否订金期数：0=否，1=是',
//  `horizontal_img` varchar(350) DEFAULT NULL COMMENT '横图题图',
//  `is_gift_box` tinyint(1) DEFAULT '0' COMMENT '礼盒发货:0=不带,1=代',
//  `shipping_conditions` tinyint(1) DEFAULT '0' COMMENT '发货条件:0=常温,1=冷链',
//  `storage_conditions` tinyint(1) DEFAULT '0' COMMENT '存储条件:0=常温,1=冰冻',
//  `delivery_time_limit` tinyint(1) DEFAULT '0' COMMENT '发货时效:0=24小时,1=72小时,2=72小时以上',
//  `design_name` varchar(10) DEFAULT NULL COMMENT '图片设计人',
//  `design_uid` int(10) DEFAULT '0' COMMENT '图片设计人id',
//  PRIMARY KEY (`id`) USING BTREE
//) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='闪购期数表';


            $header = [
                '收款公司',
                '进口商',
                '进口类型 地采/自进口',
                '类型',
                '产区/国家',
                '酒款（中文+英文）',
                '容量/规格',
                '是否带礼盒发货（礼盒和酒分开到货）',
                '单份成本',
                '闪购价格',
                '闪购数量',
                '小计',
                '利润率',
                '实际库存',
                '简码',
                '条码',
                '期数',
                '采购',
                '是否已经上传关单卫检',
                '运营备注',
                '卖点介绍',
                '是否做图 banner/开机页',
            ];

            $import_type = [0 => '自采', 1 => '地采', 2 => '跨境'];
            $is_gift_box = [0 => '不是', 1 => '是'];
            $field       = [

//                '产区/国家',
//                '酒款（中文+英文）',
//                '容量/规格',
//                '是否带礼盒发货（礼盒和酒分开到货）',
//                '单份成本',
//                '闪购价格',
//                '闪购数量',
//                '小计',
//                '利润率',
//                '实际库存',
//                '简码',
//                '条码',
//                '期数',
//                '采购',
//                '是否已经上传关单卫检',
//                '运营备注',
//                '卖点介绍',
//                '是否做图 banner/开机页',
            ];


            $items = Db::connect($db)->name('periods_flash')
                ->where([
                    ['buyer_id', 'in', [421, 354]],
                ])
                ->column('*');

            $period_ids = array_column($items, 'id');

            $temp_packages = Es::name(Es::PERIODS_PACKAGE)->where([
                ['period_id', 'in', $period_ids],
                ['is_hidden', '=', 0],
            ])->select()->toArray();
            $all_packages  = [];
            $pids          = [];
            foreach ($temp_packages as $temp_package) {
                $associated_products = json_decode($temp_package['associated_products'], true);
                $pids                = array_merge($pids, array_column($associated_products, 'product_id'));

                $temp_package['associated_products']        = $associated_products;
                $all_packages[$temp_package['period_id']][] = $temp_package;

            }
            $pids = array_values(array_unique($pids));


            $temp_periods_product_inventory = Db::connect($db)->name('periods_product_inventory')
                ->where('period', 'in', $period_ids)
                ->where("product_id", 'in', $pids)
                ->column("*");
            $all_periods_product_inventory  = [];
            foreach ($temp_periods_product_inventory as $temp_periods_product_inventor) {
                $all_periods_product_inventory[$temp_periods_product_inventor['period']][] = $temp_periods_product_inventor;
            }
            $all_products = Es::name(Es::PRODUCTS)->where([
                ['id', 'in', $pids]
            ])->select()->toArray();
            $all_products = array_column($all_products, null, 'id');

            $all_wiki_products = Db::connect('mysql_wiki_prod')
                ->name('products')->where([
                    ['id', 'in', $pids]
                ])->column('short_code,product_attachment_status', 'id');


            $wiki_product_status = [0 => "未审批", 1 => "审批中", 2 => "审批通过", 3 => "审批驳回"];
            foreach ($all_wiki_products as &$wiki_product) {
                $wiki_product['status'] = $wiki_product['short_code'] . ' ' . $wiki_product_status[$wiki_product['product_attachment_status']];
            }

            $list = [];
            foreach ($items as $item) {
                $packages = $all_packages[$item['id']] ?? null;
                if ($packages) {

                    $periods_product_inventory = $all_periods_product_inventory[$item['id']];
                    $periods_product_inventory = array_column($periods_product_inventory, null, 'product_id');
                    $products                  = [];
                    $wiki_products             = [];

                    $pac_pids = [];
                    foreach ($packages as $package) {
                        $pac_pids = array_merge($pac_pids, array_column($package['associated_products'], 'product_id'));
                    }

                    foreach (array_values(array_unique($pac_pids)) as $pac_pid) {
                        $products[]      = $all_products[$pac_pid];
                        $wiki_products[] = $all_wiki_products[$pac_pid];
                    }

                    foreach ($products as &$product) {
                        $product['cn_en_product_name'] = $product['en_product_name'] . ' ' . $product['cn_product_name'];
                    }


                    foreach ($periods_product_inventory as &$periods_product_inventor) {
                        $periods_product_inventor['d_costprice'] = $periods_product_inventor['short_code'] . ' ' . $periods_product_inventor['costprice'];
                    }


                    foreach ($packages as &$package) {

                        $tccb  = 0;
                        $tc_ps = $package['associated_products'];
                        foreach ($tc_ps as $tc_p) {

//                        if($tc_p['product_id'] == '60390'){
//                            print_r($package['associated_products']);
//                            print_r($periods_product_inventory);
//                            die;
//                        }

                            $tccb = bcadd($tccb, bcmul($tc_p['nums'],
                                $periods_product_inventory[$tc_p['product_id']]['costprice'], '3'), 2);
                        } //套餐成本价


                        $package['d_price']      = $package['package_name'] . ' ' . $package['price']; // 套餐售价
                        $package['d_price_rate'] = $package['package_name'] . ' ' . bcmul(100, bcdiv(bcsub($package['price'], $tccb, 3), $package['price'], 4), 2) . '%';
                    }


                    $var = [
                        $item['payee_merchant_name'],
                        $item['supplier'],
                        $import_type[$item['import_type']] ?? $item['import_type'],
                        implode(',', array_column($products, 'product_type_name')),
                        implode(',', array_column($products, 'regions_name_cn')),
                        implode(',', array_column($products, 'cn_en_product_name')),
                        implode(',', array_column($products, 'capacity')),
                        $is_gift_box[($item['is_gift_box'])],
                        implode(',', array_column($periods_product_inventory, 'd_costprice')),
                        implode(',', array_column($packages, 'd_price')),
                        '', //___闪购数量
                        '', //___小计
                        implode(',', array_column($packages, 'd_price_rate')),
                        '', //___实际库存
                        implode(',', array_column($periods_product_inventory, 'short_code')),
                        implode(',', array_column($periods_product_inventory, 'bar_code')),
                        implode(',', array_column($periods_product_inventory, 'bar_code')),
                        $item['id'],
                        $item['buyer_name'],
                        implode(',', array_column($wiki_products, 'bar_code')),
                        '', //___运营备注
                        '', //___卖点介绍
                        '', //___是否做图 banner/开机页
                    ];


                    $list[] = $var;
                }
            }

//            print_r($list);
//            die;


        } catch (\Exception $exception) {
            print_r('$exception->getMessage()');
            print_r($exception->getMessage());
            print_r(PHP_EOL);
            print_r($exception->getLine());

            die;
        }


        return $this->exportExcel([
            'filename'   => '闪购全部期数及相关信20231113.xlsx',
            'sheet_name' => '闪购全部期数及相关信20231113',
            'header'     => $header,
            'data'       => $list,
        ]);

    }


    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }

    /**
     * @方法描述: 香槟在售待售期数添加到专题活动
     * <AUTHOR>
     * @Date 2023/11/28 10:42
     */
    public function xb()
    {
        $types    = ['烈酒'];
        $type_arr = $find_type = [];

        $product_types = Db::connect('mysql_wiki_prod')->name('product_type')->order('fid', 'desc')->column('id,name,fid');
        $product_types = buildTree($product_types, 'children', 'fid');
        $product_types = array_column($product_types, null, 'name');
        foreach ($types as $type) {
            $type_arr[$type] = tree_to_array([$product_types[$type]]);
            $find_type       = array_merge($find_type, array_column($type_arr[$type], 'name'));
        }

        //找出所有已经过期的专题活动
        $non_activity_ids = Db::connect('mysql_marketing_prod')->name('special_activity')
            ->whereTime('end_at', '<', date('Y-m-d H:i:s'))
            ->column('id');

        $a_periods = Db::connect('mysql_marketing_prod')->name('special_activity_goods')->where('activity_id', 'not in', $non_activity_ids)->column('periods');
        $periods   = Es::name(Es::PERIODS)->where([
            ['id', 'not in', $a_periods],
            ['periods_type', 'in', [0, 1, 2, 3]],
            ['is_channel', '=', 0],
            ['product_category', 'in', $find_type],
            ['onsale_status', 'in', [1, 2]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
        ])->order(['id' => 'desc'])->select()->toArray();

        $activity_goods = [];
        foreach ($periods as $period) {

            if (array_intersect($period['product_category'], array_column($type_arr['香槟'] ?? [], 'name'))) {
                //香槟
                //【白酒】及以下子分类都放在【白酒】
                $activity_id = 50;//专题活动ID  50 香槟
                $label_id    = Db::connect('mysql_marketing_prod')->name('special_activity_label')->where('activity_id', $activity_id)->order('created_at', 'DESC')->value('id');

//            } elseif (array_intersect($period['product_category'], array_column($type_arr['白酒'] ?? [], 'name'))) {
//                //白酒 【白酒】及以下子分类都放在【白酒】
//                $activity_id = 48;//专题活动ID  48 烈酒
//                $label_id    = Db::connect('mysql_marketing_prod')->name('special_activity_label')->where('activity_id', $activity_id)->where('label_name', '白酒')->order('created_at', 'DESC')->value('id') ?? 161;

            } elseif (array_intersect($period['product_category'], array_column($type_arr['烈酒'] ?? [], 'name'))) {
                //烈酒 【烈酒】下的【威士忌、白兰地、朗姆】放在对应板块，其他的类型都放在【其他】
                $activity_id = 48;//专题活动ID  48 烈酒
                $label_id    = '162';//其他
                $p_types     = array_intersect($period['product_category'], array_column($type_arr['烈酒'], 'name'));
                $label_names = [
                    '157' => '威士忌',
                    '158' => '朗姆酒',
                    '160' => '白兰地',
                    '161' => '龙舌兰',
                ];
                foreach ($label_names as $lid => $lname) {
                    if (in_array($lname, $p_types)) {
                        $label_id = Db::connect('mysql_marketing_prod')->name('special_activity_label')->where('activity_id', $activity_id)
                            ->where('label_name', $lname)
                            ->order('created_at', 'DESC')
                            ->value('id') ?? $lid;
                        break;
                    }
                }
            }


            $activity_goods[] = [
                'periods'              => $period['id'],
                'goods_short_name'     => '',
                'product_introduction' => '',
                'activity_id'          => $activity_id,
                'label_id'             => $label_id,
                'status'               => 2,
                'sort'                 => 0,
                'created_id'           => 0,
                'created_name'         => '系统',
                'created_at'           => time(),
                'goods_type'           => ($period['periods_type'] == 11) ? 2 : 1,
            ];
        }

        $data[] = array_keys($activity_goods[0]);

        foreach ($activity_goods as $a) {
            $data[] = array_values($a);
        }

        $out = outExcel(['烈酒' => $data], '烈酒脚本数据');


        print_r($out);
        die;


//            $res = \Curl::sepcActivityGoodsAdd([
//                'periods'              => $period['id'],
//                'goods_short_name'     => '',
//                'product_introduction' => '',
//                'activity_id'          => $activity_id,
//                'label_id'             => $label_id,
//                'status'               => 2,
//                'sort'                 => 2,
//            ]);

        die('@方法描述: 香槟在售待售期数添加到专题活动');

        print_r($activity_goods);

        $res = Db::connect('mysql_marketing_prod')->name('special_activity_goods')->insertAll($activity_goods);
        var_dump($res);
        die;
    }

    //商家秒发销量同步
    public function periodsSecondMerchantsSync()
    {
        $db = 'mysql_commodities_prod';
        die('商家秒发销量同步');
        die;

        $list = Db::connect($db)->name('periods_second_merchants')
            ->where('join_period_id', 'not null')
            ->where('join_period_id', '<>', 0)
            ->where('purchased', '<>', 0)
            ->column('join_period_id,purchased');

        $list_group = [];
        foreach ($list as $item) {
            $list_group[$item['join_period_id']][] = $item['purchased'];
        }

        foreach ($list_group as $period_id => $item_g) {
            $res = Db::connect($db)->name('periods_second')->where('id', $period_id)->update(['merchant_purchased' => intval(array_sum($item_g))]);

            print_r("$period_id    $res " . PHP_EOL);
            sleep(1);
        }

        die;


    }

    //简码详情导出
    public function productExport()
    {
        $excel_data = readExcel('cpjmxq.xlsx');

        $data = [];
        foreach ($excel_data['sheet'] as $sheet) {
            $sheet_list             = $sheet['list'];
            $data[$sheet['name']][] = array_shift($sheet_list);
            $short_codes            = array_column($sheet_list, 0);

            $wiki_products = Db::connect('mysql_wiki_prod')->name('products')->where('short_code', '<>', '')->where('short_code', 'in', $short_codes)->column('short_code,cn_product_name,en_product_name', 'short_code');

            foreach ($short_codes as $short_code) {
                $data[$sheet['name']][] = [$short_code, $wiki_products[$short_code]['cn_product_name'] ?? '', $wiki_products[$short_code]['en_product_name'] ?? ''];
            }
        }

        $path = outExcel($data, '产品简码导出详情');

        print_r($path);
        die;
    }

    //首页搜索
    public function searchOptimize()
    {
        $excel_data = readExcel('syss.xlsx')['sheet1']['list'];
        $keywords   = array_column($excel_data, 0);

        $data = [];
        foreach ($keywords as $keyword) {
            $url = "https://callback.vinehoo.com/commodities/commodities/v3/periods/list?periods=&country=&creator_id=&title={$keyword}&product_type=&buyer_id=&supplier_id=&periods_type_arr=&buyer_review_status=3&product_channel=&product_category=&is_channel=&onsale_review_status=&import_type=&onsale_time_start=&onsale_time_end=&sold_out_time_start=&sold_out_time_end=&predict_shipment_time_start=&predict_shipment_time_end=&page=1&limit=10&short_code=&sales_model=";
            $res = httpCurl($url, 'get', [], '3');
            $res = json_decode($res, true);
            if (empty($res['data']['total'])) {
                $msg = "";
            } else {
                $msg = "正常(无在售商品)";
            }

            $data['cct'][] = [
                $keyword,
                $msg,
            ];
        }


        $path = outExcel($data, '首页搜索');

        print_r($path);
        die;
    }


    //简码详情导出
    public function xbExport()
    {
        $activity_id = 50; //香槟
        $filename    = $sheet_name = '香槟专题活动在售期数';

        $periods    = Db::connect('mysql_marketing_prod')->name('special_activity_goods')
            ->where('activity_id', $activity_id)->column('periods');
        $period_ids = Es::name(Es::PERIODS)->where([
            ['id', 'in', $periods],
            ['onsale_status', 'in', [1, 2]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
        ])->order(['id' => 'desc'])->field('id')->select()->toArray();
        $period_ids = array_column($period_ids, 'id');


        $aaaperiod_ids = Es::name(Es::PERIODS)->where([
            ['is_channel', '=', 0],
            ['onsale_status', 'in', [1, 2]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
            ['periods_type', 'in', ['0', '1', '2', '3']],
            ['product_category', 'in', ['香槟']],
        ])->order(['id' => 'desc'])->field('id')->select()->toArray();
        $aaaperiod_ids = array_column($aaaperiod_ids, 'id');

        $a = array_diff($aaaperiod_ids, $period_ids);

        $periods = Db::connect('mysql_marketing_prod')->name('special_activity_goods')
            ->where('activity_id', 'in', $a)->column('*');


        print_r($a);
        die;
        print_r($periods);
        die;

        print_r($period_ids);
        die;


        $items = get_periods_info($period_ids);

        $data[$sheet_name][] = ['频道', '期数', '简码', '产品名称', '售价'];

        $periods_type = [
            '0' => '闪购',
            '1' => '秒发',
            '2' => '跨境',
            '3' => '尾货',
        ];

        foreach ($items as $item) {
            $data[$sheet_name][] = [
                $periods_type[$item['periods_type']], //频道
                $item['id'], //期数
                implode(',', $item['short_code']), //简码
                $item['title'], //产品名称
                $item['price'] //售价
            ];
        }

        $path = outExcel($data, $filename);
        print_r($path);
        die;
    }

    //直播销量统计
    public function zbxl($param)
    {
        $dates = [
            "10-31",
            "11-07",
            "11-13",
            "11-14",
            "11-21",
            "11-23",
            "11-28",
            "11-30",
            "12-05",
            "12-07",
            "12-12",
            "12-14",
            "12-19"
        ];
        $year  = '2023-';
        $desc  = '直播销量统计';

        $ret[$desc][] = ['日期', '采购', '销量'];

        foreach ($dates as $day) {
            $item_day = $year . $day;

            $item_st = "{$item_day} 00:00:00";
            $item_et = "{$item_day} 23:59:59";


            //1.购买过商品名称包含"直播特卖"的商品的用户
            $zbtm_periods = Es::name(Es::PERIODS)->where([
                ['title', 'like', '*直播*']
            ])
                ->field('id,buyer_name')
                ->order(['id' => 'desc'])
                ->select()->toArray();
            $zbtm_periods = array_column($zbtm_periods, 'buyer_name', 'id');

            $zbtm_orders = Es::name(Es::ORDERS)->where([
                ['period', 'in', array_keys($zbtm_periods)],
                ['sub_order_status', 'in', [1, 2, 3]],
                ['refund_status', '=', 0],
                ['created_time', '>=', $item_st],
                ['created_time', '<=', $item_et],
            ])
                ->field('uid,package_id,period,payment_amount,created_time,order_qty')
                ->order(['id' => 'desc'])->select()->toArray();


            $packages     = Es::name(Es::PERIODS_PACKAGE)->where([
                ['id', 'in', array_values(array_unique(array_column($zbtm_orders, 'package_id')))],
            ])->order(['period_id' => 'desc'])->field('id,periods,associated_products')->select()->toArray();
            $all_packages = [];
            $pids         = [];
            $pg_nums      = [];
            foreach ($packages as &$temp_package) {
                $associated_products = json_decode($temp_package['associated_products'], true);
                $pids                = array_merge($pids, array_column($associated_products, 'product_id'));

                $temp_package['associated_products'] = $associated_products;
                $pg_nums[$temp_package['id']]        = array_sum(array_column($associated_products, 'nums'));
            }

            $o_d = [];
            foreach ($zbtm_orders as $order) {
                if (!isset($pg_nums[$order['package_id']])) {
                    print_r($order);
                    die;
                }

                $o_d[$zbtm_periods[$order['period']]][]
//                    = $pg_nums[$order['package_id']] * $order['order_qty'];
                    = $order['payment_amount'];
            }

            foreach ($o_d as $o => $d) {
                $ret[$desc][] = [$item_day, $o, round(array_sum($d), 2)];
            }
        }


        return outExcel($ret, $desc);
    }


    //商家秒发数据同步
    public function vmallInventory($param)
    {
        $db = 'mysql_commodities_prod'; //mysql_commodities_prod

        $periods = Db::connect($db)->name('periods_second_merchants')->column('join_period_id', 'id');

        $temp_periods_product_inventory = Db::connect($db)->name('periods_product_inventory')
            ->where('period', 'in', array_values($periods))
            ->column("id,period,short_code,costprice");

        $tppi_group = [];
        foreach ($temp_periods_product_inventory as $tppi) {
            $tppi_group[$tppi['period']][$tppi['short_code']] = $tppi['costprice'];
        }

        foreach ($periods as $id => $join_id) {
            $p_infos = $tppi_group[$join_id] ?? null;
            if ($p_infos === null) continue;

            foreach ($p_infos as $sc => $sc_costprice) {
                $res = Db::connect($db)->name('periods_product_inventory')->where('period', $id)->where('short_code', $sc)->update(['costprice' => $sc_costprice]);
                print_r("{$join_id}: {$id} - {$sc} - {$sc_costprice} $res" . PHP_EOL);
            }
        }

        die;
    }

    //2023年9月1日至今闪购上架的所有期数信息
    public function flash20230901()
    {
        $db       = 'mysql_commodities_prod'; //mysql_commodities_prod
        $filename = $sheet_name = '闪购期数信息';

        $periods = Es::name(Es::PERIODS)->where([
            ['is_channel', '=', 0],
            ['onsale_time', '>', '2023-09-01 00:00:00'],
            ['onsale_status', 'in', [1, 2, 3, 4]], //上架状态（0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄）
            ['periods_type', 'in', ['0']],
        ])->order(['onsale_time' => "ASC"])
            ->field('id,title,pageviews,buyer_name')
            ->select()->toArray();

        $statistics = Db::connect($db)->name('statistics_period')
            ->where('period', 'in', array_column($periods, 'id'))
            ->column('*', 'period');


        $data[$sheet_name][] = [
            '期数',
            '标题',
            '浏览量',
            '转化率',
            '销售额',
            '订单数量',
            '利润率',
            '利润值',
            '采购人',
        ];
        foreach ($periods as $datum) {
            $statistic = $statistics[$datum['id']] ?? null;
            if ($statistic === null) continue;

            $lrz = bcsub($statistic['total_order_amount'], $statistic['total_order_cost'], 2);
            $lrl = (($statistic['total_order_amount'] == 0) ? 0 : bcmul(100, bcdiv($lrz, $statistic['total_order_amount'], 4), 2)) . '%';

            $data[$sheet_name][] = [
                $datum['id'],
                $datum['title'],
                $datum['pageviews'],
                $statistic['rate'] . '%',
                $statistic['total_order_amount'],
                $statistic['sale_orders'],
                $lrl,
                $lrz,
                $datum['buyer_name'],
            ];
        }

        $path = outExcel($data, $filename);
        print_r($path);
        die;
    }


    //导出强威龙所有的期数及供应商
    public function qwlBuyer()
    {
        $db       = 'mysql_commodities_prod'; //mysql_commodities_prod
        $filename = $sheet_name = '强威龙的期数';
        $buyer_id = 486;

        $periods = Es::name(Es::PERIODS)->where([
            ['buyer_id', '==', $buyer_id],
            ['supplier', '<>', ''],
        ])->order(['onsale_time' => "ASC"])
            ->field('id,supplier')
            ->select()->toArray();

        $data[$sheet_name][] = [
            '期数',
            '供应商',
        ];

        foreach ($periods as $datum) {

            $data[$sheet_name][] = [
                $datum['id'],
                $datum['supplier'],
            ];
        }

        $path = outExcel($data, $filename);
        print_r($path);
        die;
    }


    //供应商期数
    public function supplierPeriods()
    {
        $db       = 'mysql_commodities_prod'; //mysql_commodities_prod
        $filename = $sheet_name = '供应商期数';

        $suppliers = Db::connect('mysql_wiki_prod')->name('supplier')
            ->whereOr('supplier_name', 'LIKE', "%吉香%")
            ->whereOr('supplier_name', 'LIKE', "%深圳市翡茹是文化传播有限公司%")
            ->whereOr('supplier_name', 'LIKE', "%深圳晴意国际贸易有限公司%")
            ->column('supplier_name', 'id');

        $periods = Es::name(Es::PERIODS)->where([
            ['supplier_id', 'in', array_keys($suppliers)],
        ])->order(['supplier_id' => "ASC"])
            ->field('id,supplier')
            ->select()->toArray();

        $period_ids = array_column($periods, 'id');

        $inventorys = Db::connect($db)->name('periods_product_inventory')
            ->where('period', 'in', $period_ids)
            ->column('period,short_code,costprice');

        $period_invs = [];
        foreach ($inventorys as $inv) {
            $period_invs[$inv['period']][] = $inv;
        }

        $data[$sheet_name][] = [
            '期数',
            '供应商',
            '简码',
            '成本',
        ];

        foreach ($periods as $period) {
            $p_invg = $period_invs[$period['id']] ?? [];
            $pigsc  = implode('/', array_column($p_invg, 'short_code'));
            $pigcp  = implode('/', array_column($p_invg, 'costprice'));

            $data[$sheet_name][] = [
                $period['id'],
                $period['supplier'],
                $pigsc,
                $pigcp,
            ];
        }

        $path = outExcel($data, $filename);
        print_r($path);
        die;
    }


    //批量修改期数
    public function batchPeriods()
    {
        $periods = [
            143856,
            143860,
            143862,
            143863,
            143866,
            142487,
            144292,
            144293,
            144294,
            143853,
            143935,
            144295,
            143936,
            143923,
            144298,
            143926,
            143927,
            143931,
            144369,
            144342,
            144344,
            144345,
            144370,
            144346,
            136639,
            143868,
            143869,
            144299,
            143937,
            143938,
            143878,
            144300,
            143879,
            144301,
            144303,
            143919,
            143920,
            143922,
            144368,
            144306,
            144307,
            143855,
            144308,
            144309,
            144310,
            144312,
            144313,
            144315,
            144316,
            144339,
            144340,
            144341,
            142442,
            142436,
            142439,
            126702,
            142445,
            142444,
            142443,
        ];
        print_r('批量修改期数');
        die;

        $url  = "https://callback.vinehoo.com/commodities/commodities/v3/periods/update";
        $list = Es::name(Es::PERIODS)->where([['_id', 'in', $periods]])->field('id,periods_type,express_id,onsale_time,sell_time')->select()->toArray();

        $arr = [];
        foreach ($list as $period) {
            try {
                $param = [
                    "periods_type" => $period['periods_type'],
                    "period"       => $period['id'],
                    "express_id"   => $period['express_id'],
                    "onsale_time"  => $period['onsale_time'],
                    "sell_time"    => $period['sell_time'],

                    "sold_out_time" => "2024-06-24 00:00:00",
                    "is_postpone"   => true
                ];

                $arr[$period['id']] = json_decode(httpCurl($url, 'post', json_encode($param), 10), true);
            } catch (\Exception $e) {
                print_r('ERROR : ' . $e->getMessage() . ' ' . json_encode($period) . PHP_EOL);
            }
        }

        print_r($arr);
        die;
    }

    //导出闪购非渠道商品
    public function flashPeriodsExport()
    {
        //导出今年1月1日至今，闪购上架的所有非渠道的产品。需要的字段见附件模板。
        //用于分析历史产品销售数据，为后续产品上架做指导

        $db       = 'mysql_commodities_prod'; //mysql_commodities_prod
        $filename = $sheet_name = '闪购上架的所有非渠道的产品';

        $mboxs = getOrderMysteryBox();//盲盒数据

        $periods = Db::connect($db)->name('periods_flash')
            ->whereTime('onsale_time', '>=', '2024-01-01')
            ->where('is_channel', '=', 0)
            ->column('id,title,onsale_time');

        $period_ids = array_values(array_unique(array_column($periods, 'id')));

        $packages = Db::connect($db)->name('periods_flash_set')
            ->where('period_id', 'in', $period_ids)
            ->order('price ASC')
            ->column('id,period_id,package_name,price,associated_products,is_mystery_box');

        $period_pkg = [];
        foreach ($packages as &$pkg_info) {
            $pkg_info['associated_products'] = json_decode($pkg_info['associated_products'], true);
            if ($pkg_info['is_mystery_box'] == 0) {
                $pkg_info['pkg_nums'] = array_sum(array_column($pkg_info['associated_products'], 'nums'));
            } else {
                $pkg_info['pkg_nums'] = 0;
            }
            $period_pkg[$pkg_info['period_id']][] = $pkg_info;
        }
        $packages = array_column($packages, null, 'id');

        $data[$sheet_name][] = [
            "期数",
            "标题",
            "套餐名称（最低价套餐）",
            "售价（最低套餐价）",
            "销售数量（瓶数）",
            "订单数量",
            "上架后24小时销售数量（瓶数）"
        ];

        foreach ($periods as $datum) {
            $s_time = $datum['onsale_time'];
            $e_time = $s_time + (3600 * 24);

            $orders = Db::connect('database_orders_prod')->name('flash_order')->alias('f')
                ->join('order_main m', 'f.main_order_id=m.id')
                ->where('f.period', $datum['id'])
                ->where('f.sub_order_status', 'in', [1, 2, 3])//子订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
                ->where('f.refund_status', 'in', [0, 1, 3])
                ->order('f.created_time ASC')
                ->column('f.id,f.package_id,f.created_time,f.order_qty,m.main_order_no');

            $total_orders_product_nums = $one_day_product_nums = 0;

            foreach ($orders as $order) {
                $order_pkg      = $packages[$order['package_id']];
                $order_pkg_nums = $order_pkg['pkg_nums'] ?? 0;
                if (!empty($order_pkg)) {
                    if ($order_pkg['is_mystery_box'] == 1) {
                        $product_items  = $mboxs["{$order['main_order_no']}_{$order['package_id']}"] ?? [];
                        $order_pkg_nums = array_sum(array_column($product_items, 'nums'));
                    }
                }
                $order_product_num         = bcmul($order['order_qty'], $order_pkg_nums);
                $total_orders_product_nums = bcadd($total_orders_product_nums, $order_product_num); //订单总瓶数
                if ($order['created_time'] >= $s_time && $order['created_time'] <= $e_time) {
                    $one_day_product_nums = bcadd($one_day_product_nums, $order_product_num); //上架后24小时销售数量（瓶数）
                }
            }

            $min_price_package   = $period_pkg[$datum['id']][0] ?? [];
            $data[$sheet_name][] = [
                $datum['id'],
                $datum['title'],
                $min_price_package['package_name'] ?? '',
                $min_price_package['price'] ?? '',
                $total_orders_product_nums,
                count($orders),
                $one_day_product_nums
            ];
        }

        $path = outExcel($data, $filename);
        print_r($path);
        die;
    }


}



