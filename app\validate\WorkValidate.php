<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class WorkValidate
 * @package app\validate
 */
class WorkValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|id' => 'require|number',  //id

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name',],
        'edit'   => [ 'id',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', ])->remove('id', 'require')
            ;
    }




}