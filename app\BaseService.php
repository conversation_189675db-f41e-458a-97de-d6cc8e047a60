<?php

namespace app;

use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Log;

abstract class BaseService
{
    /**
     * model
     * @var null
     */
    protected $model        = null;
    protected $select_with  = [];
    protected $select_order = ['id' => 'desc'];
    protected $hidden_field = [];
    protected $validate     = null;
    use ApiResponse;

    /**
     * @方法描述: 插入一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            $this->model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function edit($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 修改
        Db::startTrans();
        try {
            $model = $this->model->find($param['id']);
            if ($model === null) {
                throw new Exception("未找到数据, 请检查参数.");
            }

            $model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 分页获取列表数据
     * <AUTHOR>
     * @Date 2022/8/10 14:52
     * @param $param
     * @return array|bool|mixed
     */
    public function list($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        #region where查询条件,分页,查询数据的处理
        $where = $this->builderWhere($param);

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        $pagestart = ($page - 1) * $limit;
        $field     = $param['fields'] ?? "*";
        //endregion

        //region 分页获取数据
        Db::startTrans();
        try {
            $model = $this->model->field($field)->with($this->select_with)->where($where)->limit($pagestart, $limit);
            if (!empty($this->select_order)) {
                foreach ($this->select_order as $sort_field => $sort) {
                    $model->order($sort_field, $sort);
                }
            }
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }

            $list = $model->select();

            $total = $this->model->where($where)->count();
            $data  = [
                'list'  => $list,
                'total' => $total
            ];

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($this->listDataProcessing($data));
    }

    /**
     * @方法描述: 获取详情
     * <AUTHOR>
     * @Date 2022/8/10 15:15
     * @param $param
     * @return bool|mixed
     */
    public function detail($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 获取数据
        Db::startTrans();
        try {
            $model = $this->model->with($this->select_with);
            if (!empty($this->hidden_field)) {
                $model->hidden($this->hidden_field);
            }
            $data = $model->find($param['id']);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('查询数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('查询数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($data);
    }

    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function status($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 修改
        Db::startTrans();
        try {
            $model = $this->model->find($param['id']);
            if ($model === null) {
                throw new Exception("未找到数据, 请检查参数.");
            }
            $data           = [];
            $data['status'] = $param['status'];

            if (!empty($param['vh_uid'])) {
                $data['vh_uid'] = $param['vh_uid'];
            }
            if (!empty($param['vh_vos_name'])) {
                $data['vh_vos_name'] = $param['vh_vos_name'];
            }

            $model->save($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新状态失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新状态失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }


    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res   = 0;
            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }


    /**
     * @方法描述: 自定义处理列表数据
     * <AUTHOR>
     * @Date 2022/8/10 15:45
     * @param $data
     * @return mixed
     */
    public function listDataProcessing($data)
    {
//        if ($data['total']) {
//            $list = $data['list'];
//
//            foreach ($list as $item) {
//
//            }
//
//            $data['list'] = $list;
//        }
        return $data;
    }

    #############################################################################

    /**
     * @方法描述: 验证数据
     * <AUTHOR>
     * @Date 2022/8/3 13:44
     */
    protected function check($scene, $data)
    {
        try {
            validate($this->validate)
                ->scene($scene)
                ->check($data);
            return true;
        } catch (ValidateException $e) {
            return $this->failed($e->getError(), ErrorCode::PARAM_ERROR);

        }
    }

    protected function builderWhere($param)
    {
    }

}
