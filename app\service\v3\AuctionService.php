<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Auction;
use app\validate\AuctionValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 拍卖
 * Class AuctionService
 * @package app\service\v3
 */
class AuctionService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Auction;
        $this->validate    = AuctionValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/17 10:09
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }


    public function encryptUser()
    {
        $db  = [
            'acution' => [
                'dev'  => 'dev_mysql_auction',
                'prod' => 'pord_mysql_auction',
            ]
        ];
        $env = 'prod'; //prod dev

        die($env . '_上传代码后执行一次, 执行前请备份');


        $db_auction = $db['acution'][$env];

        $list     = Db::connect($db_auction)->name('transfer_log')->column('id,identity,name');
        $dec_data = [];
        foreach ($list as $item) {
            !empty($item['identity']) && $dec_data[] = $item['identity'];
            !empty($item['name']) && $dec_data[] = $item['name'];
        }

        $enc_data = \Curl::cryptionDeal(array_values(array_unique($dec_data)), 'E');

        $update_id_arr = [];


        foreach ($list as $var) {
            $update_id_arr[$var['id']] =
                Db::connect($db_auction)->name('transfer_log')->where('id', $var['id'])->update([
                    'identity' => $enc_data[$var['identity']] ?? $var['identity'],
                    'name'     => $enc_data[$var['name']] ?? $var['name'],
                ]);
        }

        print_r($update_id_arr);
        die;


        $list = Db::connect($db_auction)->name('user')->column('uid,realname,id_card,alipay_account');

        $dec_data = [];
        foreach ($list as $item) {
            !empty($item['realname']) && $dec_data[] = $item['realname'];
            !empty($item['id_card']) && $dec_data[] = $item['id_card'];
            !empty($item['alipay_account']) && $dec_data[] = $item['alipay_account'];
        }

        $enc_data = \Curl::cryptionDeal($dec_data, 'E');

        $update_id_arr = [];

        foreach ($list as $var) {
            if (!(empty($var['realname']) && empty($var['id_card']) && empty($var['alipay_account']))) {
                $update_id_arr[$var['uid']] =
                    Db::connect($db_auction)->name('user')->where('uid', $var['uid'])->update([
                        'realname'       => $enc_data[$var['realname']] ?? $var['realname'],
                        'id_card'        => $enc_data[$var['id_card']] ?? $var['id_card'],
                        'alipay_account' => $enc_data[$var['alipay_account']] ?? $var['alipay_account'],
                    ]);
            }
        }

        print_r($update_id_arr);
        die;
    }


    public function wmsCancelOrder()
    {
        $db  = [
            'acution' => [
                'dev'  => 'dev_mysql_auction',
                'prod' => 'pord_mysql_auction',
            ]
        ];
        $env = 'prod'; //prod dev

        die($env . '_上传代码后执行一次, 执行前请备份');


        $db_auction = $db['acution'][$env];

//        $goodsids = Db::connect($db_auction)->name('goods')
//            ->where('onsale_status', 'in', [0,2,1])
//            ->where('onsale_review_status', '=', 3)
//            ->where('issue_type', '=', 0)
//            ->where('uec_code', 'in', ['318', '319', '309'])
//            ->column('id');
//
//        $list = Db::connect($db_auction)->name('order_temporary')
//            ->where('goods_id', 'in', $goodsids)
//            ->where('status', '2')
//            ->field("id,goods_id,order_no,status")
//            ->select()->toArray();


        $goodsids = Db::connect($db_auction)->name('goods')
            ->where('uec_code', '=', '000')
            ->column('id');

        $goods_infos = Db::connect($db_auction)->name('order_temporary')
            ->where('status', 0)
            ->where('goods_id', 'not in', $goodsids)
            ->column('id,order_no,goods_id,status');

        $success = [];
        $fail    = [];
        foreach ($goods_infos as $info) {
            try {
                \Curl::receiveOrderSync(['orderno' => $info['order_no']]);
                $success[] = $info['id'];
            } catch (\Exception $e) {
                $fail[] = $info['id'];
            }
        }

        $total = Db::connect($db_auction)->name('order_temporary')
            ->where('id', 'in', $success)
            ->update(['status' => 2, 'push_wms_status' => 1, 'update_time' => time(), 'remarks' => '批量处理']);

        print_r($success);
        print_r($fail);
        print_r($total);
        die;


        //  `goods_id` int(11) NOT NULL COMMENT '拍品ID',
        //  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-待创建订单 1-已创建订单 2-已撤单 3-撤单失败',
        //  `push_wms_status` tinyint(1) DEFAULT '0' COMMENT '发货仓推送状态：0-未推送 1-推送成功 2-推送失败 3-不推送',


        print_r($goods_infos);
        die;

    }


}



