<?php


class Dewu
{
    protected $app_secret = '';
    protected $host       = null;
    protected $app_key    = null;

    public function __construct()
    {
        $this->app_secret = env('DEWU.APPSECRET', '');
        $this->host       = env('DEWU.HOST');
        $this->app_key    = env('DEWU.APP_KEY');
    }

    /**
     * @方法描述: 增量拉取订单（直发类型）
     * <AUTHOR>
     * @Date 2023/2/20 10:49
     * @param $param
     * @return array|mixed
     * @throws \think\Exception
     */
    public function orderGenericIncrement($param)
    {
        $request_param = [];
        try {
            $url = $this->host . '/dop/api/v1/order/brand_deliver/increment';

            $page_size = 30;
            $page_no   = 1;
            $orders    = [];
            do {
                //将一段时间内订单循环取出来
                $request_param         = [
                    'app_key'        => $this->app_key,
                    'timestamp'      => getMillisecond(),
                    'page_size'      => $page_size,
                    'page_no'        => $page_no,
                    'start_modified' => $param['start_modified'],
                    'end_modified'   => $param['end_modified'],
                ];
                $request_param['sign'] = $this->createSign($request_param); //签名

                $code   = httpCurl($url, 'post', json_encode($request_param), 3);
                $result = json_decode($code, true);

                if (!$result || $result['code'] != 200) {
                    throw new Exception($result['msg'] ?? '得物增量拉取订单错误');
                }

                $data   = $result['data'];
                $orders = array_merge($orders, ($data['orders'] ?? []));
                $page_no++;

            } while ((($data['total_results'] - ($data['page_no'] * $data['page_size'])) > 0));

        } catch (Exception $e) {
            \think\facade\Log::error("得物增量拉取订单错误: request: " . json_encode([$url, 'post', $request_param]) . ' response: ' . ($code ?? ''));
            throw new \think\Exception($e->getMessage());
        }
        return $orders;
    }


    /**
     * @方法描述: 生成签名
     * <AUTHOR>
     * @Date 2023/2/20 10:49
     * @param $paramArr
     * @return string
     */
    private function createSign($paramArr)
    {
        ksort($paramArr);
        foreach ($paramArr as $key => $val) { //过滤空数据项
            if (is_array($val)) {
                $paramArr[$key] = str_replace(['[', ']'], ['', ''], json_encode($val, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            }
        }

        $sign_str = http_build_query($paramArr, NULL, '&') . $this->app_secret;

        return strtoupper(md5($sign_str));
    }

}