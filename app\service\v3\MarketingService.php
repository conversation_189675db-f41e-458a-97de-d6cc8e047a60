<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Marketing;
use app\validate\MarketingValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 营销活动
 * Class MarketingService
 * @package app\service\v3
 */
class MarketingService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Marketing;
        $this->validate    = MarketingValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/07/05 17:04
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    public function couponQueue($param)
    {
        $json = <<<'json'

json;
        //发券失败的的JSON

        die("确认要发优惠券吗?");

        $header[] = 'vinehoo-client: tp6-marketing-conf';

        foreach (json_decode($json, true) as $item) {
            $data                 = json_decode($item['queue_data'], true);
            $data['coupon_id'][0] = intval($data['coupon_id'][0]);


            $url = env('ITEM_PROD.QUEUE_URL');

            $code   = httpCurl($url, 'post', json_encode([
                'exchange_name' => 'activity',
                'routing_key'   => 'activity.coupon.rabbits',
                'data'          => base64_encode(json_encode($data)),
            ]), 3, $header);
            $result = json_decode($code, true);

            Log::write('queuePush  ' . $code);

            if (!$result || $result['error_code'] != 0) {
                throw new Exception('更新加入队列错误' . $code);
            }
        }


    }


    /**
     * @方法描述: 沉默用户活动 指定用户ID数据脚本
     * <AUTHOR>
     * @Date 2023/8/15 13:59
     */
    public function activityTargetUser()
    {
//        Db::connect()

        $data  = $this->readExcel('1.xlsx');
        $users = $data['sheet1']['list'];
        unset($users[0]);

        $uids = array_column(array_values($users), 0); // 全部新增的uid

        $exist_uids = Db::connect('mysql_marketing_prod')->name('special_activity_target_user')
            ->where('special_activity_id', 999999)
            ->column('vh_uid');

        $validate_uids = array_diff($uids, $exist_uids);


        $data = [];
        foreach ($validate_uids as $i_uid) {
            $data[] = ['special_activity_id' => 999999, 'vh_uid' => $i_uid];
        }

        var_dump(count($data));

        $res = Db::connect('mysql_marketing_prod')->name('special_activity_target_user')->insertAll($data);
        var_dump($res);
        print_r($data);
        die;
    }


    /**
     * @方法描述: 读取Excel
     * <AUTHOR>
     * @Date 2022/9/6 10:24
     * @param $filename
     * @return array
     */
    public function readExcel($filename)
    {
        $path      = app()->getRuntimePath() . DIRECTORY_SEPARATOR . 'other';
        $config    = ['path' => $path];
        $excel     = new \Vtiful\Kernel\Excel($config);
        $sheetList = $excel->openFile($filename)->sheetList();
        $data      = [
            'path'      => $path,
            'file_name' => $filename,
        ];

        $i = 1;
        foreach ($sheetList as $sheetName) {

            $sheetData = $excel
                ->openSheet($sheetName)
                ->getSheetData();

            $data['sheet' . $i] = [
                'name' => $sheetName,
                'list' => $sheetData,
            ];
            $i++;
        }
        return $data;
    }
}



