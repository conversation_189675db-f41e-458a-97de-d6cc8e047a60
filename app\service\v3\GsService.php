<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Gs;
use app\model\MonthStatement;
use app\validate\GsValidate;
use think\facade\Db;

/**
 * 公社
 * Class GsService
 * @package app\service\v3
 */
class GsService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Gs;
        $this->validate    = GsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/04/23 11:58
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    //导出公社第一季度用户数据
    public function exportUserData($param)
    {
        #region 查询数据
        $field  = [
            'id',
            'nickname',
            'phone',
            'addtime',
            'birthday',
            'introduce',
            'level',
            'level_tag',
            'level_time',
            'level_overdue_time',
            'province_name',
            'registered_ip',
        ];
        $header = [
            'ID',
            '昵称',
            '手机号',
            '添加时间',
            '生日',
            '个人介绍',
            '等级',//0：无等级；1：企业普通职位；2：企业高管
            '等级标签',// 等级标签【1：中小型酒企职员 ;2:中小型酒企高管;3:大型酒企职员;4:大型酒企高管】
            '认证时间',
            '认证后福利过期时间',
            '注册省',
            '注册城市',
        ];
        $list   = Db::connect('mysql_gs_prod')->name('user')->field($field)->where(function ($query) {
            $query->whereBetweenTime('addtime', '2023-01-01 00:00:00', '2023-03-31 23:59:59');
            $query->where('power', 0);
        })->select()->toArray();

        $levels = [
            0 => '无等级',
            1 => '企业普通职位',
            2 => '企业高管'
        ];

        $level_tags = [
            1 => '中小型酒企职员',
            2 => '中小型酒企高管',
            3 => '大型酒企职员',
            4 => '大型酒企高管',
        ];


        $ip_addr = \Curl::naliIp(['ips' => array_column($list, 'registered_ip')]);

        foreach ($list as &$item) {
            $item['addtime'] && $item['addtime'] = date("Y-m-d H:i:s", $item['addtime']);
            $item['level_time'] && $item['level_time'] = date("Y-m-d H:i:s", $item['level_time']);
            $item['level_overdue_time'] && $item['level_overdue_time'] = date("Y-m-d H:i:s", $item['level_overdue_time']);
            $item['level']     = $levels[$item['level']] ?? '';
            $item['level_tag'] = $level_tags[$item['level_tag']] ?? '';


            $ip_city_name      = empty($ip_addr[$item['registered_ip']])
                ? ''
                :
                ((count($ip_addr[$item['registered_ip']]) <= 2)
                    ? $ip_addr[$item['registered_ip']][0]
                    :
                    ((count($ip_addr[$item['registered_ip']]) == 3) ? $ip_addr[$item['registered_ip']][1] : $ip_addr[$item['registered_ip']][2]));
            $ip_city_name      = ($ip_city_name == '内网IP') ? '未知' : $ip_city_name;
            $item['city_name'] = $ip_city_name;
            unset($item['registered_ip']);
        }
        #endregion

        #region 生成excel
        $path = app()->getRuntimePath() . 'gs' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 775, true);
        }
        $excel = new \Vtiful\Kernel\Excel(['path' => $path]);
        $excel->fileName('公社2023年第一季度新增用戶.xlsx');

        $filePath = $excel
            ->header($header)
            ->data($list)
//            ->addSheet('eeee')
//            ->header($header)
//            ->data($list)
            ->output();
        #endregion


        print_r($filePath);
        die;
        print_r(Db::connect('mysql_gs_prod')->getLastSql());
        die;

    }

    public function companyDataExport($param)
    {
        #region 查询数据
        $field  = [
            'a.id',
            'a.company_name',
            'a.company_industry',
            'a.company_logo',
            'c.area_name as p',
            'd.area_name as c',
            'e.area_name as a',
            'a.address',
            'a.company_email',
            'a.license',
            'a.company_phone',
            'b.phone',
            'a.tag_id',
        ];
        $header = [
            '公司ID',
            '公司名称',
            '行业类目',
            'logo',
            '省',
            '市',
            '区',
            '详细地址',
            '公司邮箱',
            '营业执照',
            '公司手机号',
            '用户手机号',
            '标签',
        ];

        $tags = Db::connect('mysql_gs_prod')->name('user_tag')->column('tag_name', 'id');
        $list = Db::connect('mysql_gs_prod')
            ->name('company')->alias('a')
            ->leftJoin('user b', 'a.id=b.cid')
            ->leftJoin('regional c', 'a.province=c.id')
            ->leftJoin('regional d', 'a.city=d.id')
            ->leftJoin('regional e', 'a.area=e.id')
            ->field($field)
            ->select()->toArray();

        $export_data = [];
        foreach ($list as $item) {
            $item_tags = [];
            if (!empty($item['tag_id'])) {
                foreach (explode(',', $item['tag_id']) as $item_tag_id) {
                    $item_tags[] = $tags[$item_tag_id] ?? $item_tag_id;
                }
                $item['tag_id'] = implode(',', $item_tags);
            }
            $export_data[] = array_values($item);
        }
        #endregion

        #region 生成excel
        $path = app()->getRuntimePath() . 'gs' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 775, true);
        }
        $excel = new \Vtiful\Kernel\Excel(['path' => $path]);
        $excel->fileName('公社公司数据导出.xlsx');

        $filePath = $excel
            ->header($header)
            ->data($export_data)
//            ->addSheet('eeee')
//            ->header($header)
//            ->data($list)
            ->output();
        #endregion


        print_r($filePath);
        die;
        print_r(Db::connect('mysql_gs_prod')->getLastSql());
        die;

    }

    public function adminsExport($param)
    {
        #region 查询数据
        $header = ['手机号', '昵称', '权限', '最后发送验证码时间'];

        $users = Db::connect('mysql_gs_prod')->name('user')
            ->where('power','in', [1,2])
            ->order('power', 'DESC')
            ->column('nickname,phone,power');

        $send_times =  Db::connect('mysql_gs_prod')->name('user_phone_code_log')
            ->where('phone','in', array_column($users,'phone'))
            ->order('addtime', 'DESC')
            ->column('phone,addtime');

        $send_times_g = array_group($send_times,'phone');




        $export_data = [];
        $power = [1 => '公司', 2 => '超级'];
        foreach ($users as $item) {
            $export_data[] = [
                $item['nickname'],
                $item['phone'],
                $power[$item['power']],
                $send_times_g[$item['phone']]['0']['addtime']?? ''
            ];
        }
        #endregion

        #region 生成excel
        $path = app()->getRuntimePath() . 'gs' . DIRECTORY_SEPARATOR;
        if (!is_dir($path)) {
            mkdir($path, 775, true);
        }
        $excel = new \Vtiful\Kernel\Excel(['path' => $path]);
        $excel->fileName('公社管理员数据导出.xlsx');

        $filePath = $excel
            ->header($header)
            ->data($export_data)
//            ->addSheet('eeee')
//            ->header($header)
//            ->data($list)
            ->output();
        #endregion


        print_r($filePath);
        die;
        print_r(Db::connect('mysql_gs_prod')->getLastSql());
        die;

    }

}



