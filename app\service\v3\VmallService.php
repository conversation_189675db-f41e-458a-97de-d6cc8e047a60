<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Vmall;
use app\validate\VmallValidate;
use think\facade\Cache;
use think\facade\Db;

/**
 * 商家秒发
 * Class VmallService
 * @package app\service\v3
 */
class VmallService extends BaseService
{
    const REDIS_SERVICE_RADIUS = "vmall.selfdelivery.range"; //redis 店铺服务范围KEY

    public function __construct()
    {
        $this->model       = new Vmall;
        $this->validate    = VmallValidate::class;
        $this->select_with = [];
    }

    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/04/07 11:29
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    public function redisServiceRadius($param)
    {
        $list = Db::connect('mysql_dev')->name('shops')->column('visual_range', 'lonlat');

        // !!! 注意REDIS 配置
        $handler = Cache::store('cache_vmall_dev')->handler();
        $handler->del(self::REDIS_SERVICE_RADIUS);
        foreach ($list as $lonlat => $visual_range) {
            $handler->hSet(self::REDIS_SERVICE_RADIUS, $lonlat, $visual_range * 1000);
        }
    }

    public function htmlToMd($param)
    {
        $str = <<<str
参数	字段描述	类型/是否必传	说明
issOrderNo	闪送订单号	String(20)/YES	订单计费接口获取
str;


        $data = explode(PHP_EOL, $str);
        foreach ($data as &$datum11) {
            $datum11 = explode('	', $datum11);
        }
        $header = array_shift($data);

//        array_unshift($data, ["mid", "int/YES", "商家ID", "7"]);

        $string_arr[] = "|" . implode("|", $header) . "|";
        $string_arr[] = "|:-";

        foreach ($data as $datum12) {
            $string_arr[] = "|" . implode("|", $datum12) . "|";
        }
        print_r(implode(PHP_EOL, $string_arr));


        die;
    }

    public function shopsLonglatsData($param)
    {
        $shops     = Db::connect('mysql_vmall_pord')->name('shops')->where('mid','not in', [31])->column('*');
        $merchants = Db::connect('mysql_vmall_pord')->name('merchants')->column('*', 'id');

        $data = [];
        foreach ($shops as $shop) {
            $data[] = [
                'shop_id'      => $shop['id'],
                'mid'          => $shop['mid'],
                'cid'          => $merchants[$shop['mid']]['cid'],
                'aid'          => $merchants[$shop['mid']]['aid'],
                'visual_range' => intval(($shop['visual_range'] ?? 0) * 1000),
                'lonlat'       => Db::raw("POINT({$shop['lonlat']})"),
                'status'       => (($merchants[$shop['mid']]['all_shop_status'] && $merchants[$shop['mid']]['shop_status'] && $shop['status']) ? 1 : 0)
            ];
        }

        try {
            $res = Db::connect('mysql_vmall_pord')->name('shops_longlats')->insertAll($data);
        } catch (\Exception $exception) {
            var_dump("ERROR :",
                $exception->getMessage(),
                Db::connect('mysql_vmall_pord')->getLastSql()
            );
            die;

        }

        var_dump("SUCCESS", $res);
        die;
        print_r($data);
        die;
        print_r($merchants);
        die;
        print_r($list);
        die;


    }

    public function jfbsp($param)
    {
        $sid = 16; //解放碑店

        $list = Db::connect('pord_mysql_osms_store')->name('sales_goods')->alias('g')
            ->join('sales_collocation c', 'c.goods_id = g.id')
            ->where('g.sid', $sid)
            ->where('c.is_enable', 1)
            ->field('g.id,g.goods_name,c.pids')
            ->select()->toArray();

        $export = [];
        foreach ($list as $item){
            $pids = json_decode($item['pids'], true);
            $p_nums = array_values(array_unique(array_column($pids,'nums')));
            if([1] != $p_nums){
                $export[] = [
                    $item['id'],
                    $item['goods_name'],
                    implode(',', $p_nums)
                ];
            }
        }

        $header = ['商品ID', '商品名称', '套餐数量详情'];


        return $this->exportExcel([
            'filename'   => '解放碑店商品.xlsx',
            'sheet_name' => '解放碑店',
            'header'     => $header,
            'data'       => $export,
        ]);

    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }


}



