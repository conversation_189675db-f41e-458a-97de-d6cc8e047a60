<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Wms;
use app\validate\WmsValidate;
use think\facade\Db;

/**
 * 萌芽
 * Class WmsService
 * @package app\service\v3
 */
class WmsService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Wms;
        $this->validate    = WmsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/06/15 14:23
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    public function dataSeparation($param)
    {
        $env   = 'dev'; //dev (测试环境)  or  prod (正式环境)
        $go_db = 1;//云酒
        $to_db = [2, 3, 4]; //科技 松鸽 木兰朵


        #region 基础数据定义
        $db                 = [
            '1' => "{$env}_mysql_wms_stock_1", //云酒
            '2' => "{$env}_mysql_wms_stock_2", //科技
            '3' => "{$env}_mysql_wms_stock_3", //松鸽
            '4' => "{$env}_mysql_wms_stock_4", //木兰朵
        ]; //数据库名 云酒 科技 松鸽 木兰朵
        $fictitious_company = [
            "云酒"                   => 1,
            "佰酿云酒（南通常货仓）"   => 5,
            "佰酿云酒（南通闪购仓）"   => 1,
            "佰酿云酒（南通淘宝）"     => 2,
            "佰酿云酒（南通酒云京东）" => 2,
            "佰酿云酒（南通拼多多）"   => 1,
            "视频电商仓"             => 2,
            "佰酿云酒（南通法南京东）" => 1,
            "佰酿云酒（南通法南天猫）" => 1,
            "佰酿云酒（天猫旗舰店）"   => 2,
            "佰酿云酒（南通秒发仓）"   => 2,
            "重庆古丝缇跨境仓"       => 6,
            "保税区直发仓"           => 6,
            "南通OT仓"               => 2,
            "新媒体-南通仓"          => 2,
            "广州南沙跨境仓"         => 6,
            "佰酿云酒（南通猫超）"     => 1,
            "佰酿云酒（BD仓）"         => 1,
            "桃公子-南通仓"          => 2,
            "佰酿云酒（代发仓）"       => 5,
            "渠道连锁店-南通仓"      => 2,
            "佰酿云酒（重庆仓）"       => 1,
            "微店-酒云旗舰店"        => 1,
            "布拉德国际-南通仓"      => 1,
            "佰酿云酒（科技）南通仓"   => 2,
            "双系统切换盘点专用仓"   => 6,
            "欧美保代管仓（南通）"     => 6,
            "南通破损仓"             => 5,
            "兔子窝"                 => 6,
            "兔子窝01"               => 6,
            "兔子窝残损仓"           => 6,
            "存疑库存"               => 1,
            "木兰朵"                 => 4,
            "木兰朵（南通仓）"         => 4,
            "酝享"                   => 6,
            "酝享仓"                 => 6,
            "佰酿云酒（天猫国际）"     => 1,
            "次品待入仓"             => 1,
            "布拉德国际-南通"        => 1,
            "南通次品仓"             => 5,
            "兔头商店-南通仓"        => 1,
            "老外买酒-南通仓"        => 2,
            "美酒线上仓"             => 1,
            "美酒线上南通仓"         => 1,
            "佰酿云酒（南通食品仓）"   => 5,
            "跨境退货仓"             => 6,
            "拓展事业部"             => 1,
            "佰酿云酒（拍卖仓）"       => 2,
            "佰酿云酒（拍卖代管仓）"   => 6,
            "酒云网抖音旗舰店仓"     => 1,
            "酒云网淘宝拍卖店仓"     => 2,
            "云酒科技（南通猫超）"     => 2,
            "快团团南通仓"           => 1,
            "松鸽酒业"               => 3,
            "松鸽酒业南通仓"         => 3,
            "云酒科技（链多多南通仓）" => 2,
            "小红书-南通仓"          => 2,
            "云酒科技"               => 2,
            "云酒科技（拼多多南通仓）" => 2,
            "供应商赠品仓"           => 5
        ]; //仓库对应公司
        $company_name       = ['1' => '云酒', '2' => '科技', '3' => '松鸽', '4' => '木兰朵',];
        $now                = time();
        #endregion 基础数据定义

        #region 数据处理
        #region 变量初始化
        $fictitiousid_company = []; // 仓库ID => 公司ID
        $fictitious_ids       = []; //需要转移数据的全部仓库ID

        $all_inster   = []; //全部需要插入的数据
        $all_update   = []; //全部需要更新的数据
        $up_ids       = []; //xx
        $up_locations = [];
        #endregion 变量初始化
        #region 仓库分组
        $fictitious = Db::connect($db[$go_db])->name('fictitious')->column('fictitious_name', 'fictitious_id');
        foreach ($fictitious as $id => $name) {
            $conpany_id                = $fictitious_company[$name] ?? 999;
            $fictitiousid_company[$id] = $conpany_id;
            if (in_array($conpany_id, $to_db)) {
                $fictitious_ids[] = $id;
            }
        }
        #endregion 仓库分组

        #region 调试用的
        /*
        $arr = [];
        foreach ($fictitiousid_company  as $k => $v){
            if(in_array($v,[2,3,4])){
                $arr[$v][] = $k;
            }
        }
        foreach ($arr as &$item){
            $item = implode(',', $item);
        }
        print_r($arr);die ;
        */
        #endregion

        #region 所有需要移动的虚拟仓
        $fictitious_goods = Db::connect($db[$go_db])->name('fictitious_goods')
            ->where('fictitious_id', 'in', $fictitious_ids)
            ->column('*');

        $location_goods_group = [];
        $bar_codes            = array_column($fictitious_goods, 'bar_code');// 全部条码
        $location_goods       = Db::connect($db[$go_db])->name('location_goods')
            ->alias('lg')
            ->join('location l', 'lg.location_id = l.location_id')
            ->where('lg.bar_code', 'in', $bar_codes)
            ->column('lg.*,l.quality_attribute'); //品质属性：1良品，2次品

        foreach ($location_goods as $location_good) {
            $quality_attribute = $location_good['quality_attribute'];
            unset($location_good['quality_attribute']);
            $location_goods_group[$location_good['bar_code']][$quality_attribute][] = $location_good;
        }

        foreach ($fictitious_goods as $fictitious_good_key => $fictitious_good) {
            $company_id      = $fictitiousid_company[$fictitious_good['fictitious_id']]; //公司ID
            $good_fake_count = intval($fictitious_good['goods_count'] + $fictitious_good['fake_count']); //调整总数据
            $goods_count     = intval($fictitious_good['goods_count']); //良品
            $fake_count      = intval($fictitious_good['fake_count']); //次品

            $location_i      = $location_goods_group[$fictitious_good['bar_code']] ?? [];
            $location_items1 = $location_i[1] ?? []; //品质属性：1良品，
            $location_items2 = $location_i[2] ?? [];//2次品

            //1良品
            foreach ($location_items1 as &$location_1) {
                $min_deduct_nums = min($location_1['available_count'], $location_1['actual_count']);// 最大可扣库存

                //良品
                if (($goods_count > 0) && ($min_deduct_nums > 0)) {
                    $current_deduct = min($goods_count, $min_deduct_nums); //本次实际扣除
                    //源库出库日志
                    $all_inster[$go_db]['location_open_log'][] = [
                        'location_id' => $location_1['location_id'], //虚拟仓ID
                        'bar_code'    => $location_1['bar_code'], //商品条码
                        'type'        => 1, //1减少库存，2增加库存
                        'nums'        => $current_deduct, //商品数量
                        'available'   => 1, //是否操作可用库存：0否，1是
                        'actual'      => 1, //是否操作实际库存：0否，1是
                        'open_desc'   => "【良品】迁移库存至【{$company_name[$company_id]}】,商品可用和实际库存数量: {$current_deduct}", //操作说明
                        'create_time' => $now, //新增时间
                        'admin_name'  => '系统', //操作人名称
                        'admin_id'    => 0, //操作人id
                    ];
                    //新库入库日志
                    $all_inster[$company_id]['location_open_log'][] = [
                        'location_id' => $location_1['location_id'], //虚拟仓ID
                        'bar_code'    => $location_1['bar_code'], //商品条码
                        'type'        => 2, //1减少库存，2增加库存
                        'nums'        => $current_deduct, //商品数量
                        'available'   => 1, //是否操作可用库存：0否，1是
                        'actual'      => 1, //是否操作实际库存：0否，1是
                        'open_desc'   => "【{$company_name[$go_db]}】迁移【良品】库存至本库,商品可用和实际库存数量: {$current_deduct}", //操作说明
                        'create_time' => $now, //新增时间
                        'admin_name'  => '系统', //操作人名称
                        'admin_id'    => 0, //操作人id
                    ];
                    //新库入库
                    if (empty($all_inster[$company_id]['location_goods'][$location_1['seat_location_id']])) {
                        $in_data                    = $location_1;
                        $in_data['available_count'] = $current_deduct;
                        $in_data['actual_count']    = $current_deduct;
                    } else {
                        $in_data                    = $all_inster[$company_id]['location_goods'][$location_1['seat_location_id']];
                        $in_data['available_count'] = $in_data['available_count'] + $current_deduct;
                        $in_data['actual_count']    = $in_data['actual_count'] + $current_deduct;
                    }
                    $all_inster[$company_id]['location_goods'][$location_1['seat_location_id']] = $in_data;

                    //源库出库
                    $location_1['available_count']                 = $location_1['available_count'] - $current_deduct;
                    $location_1['actual_count']                    = $location_1['actual_count'] - $current_deduct;
                    $up_locations[$location_1['seat_location_id']] = $location_1;
                    $goods_count                                   = $goods_count - $current_deduct;
                }
            }


            //2次品
            foreach ($location_items2 as &$location_2) {
                $min_deduct_nums = min($location_2['available_count'], $location_2['actual_count']);// 最大可扣库存

                //次品
                if (($fake_count > 0) && ($min_deduct_nums > 0)) {
                    $current_deduct = min($fake_count, $min_deduct_nums); //本次实际扣除
                    //源库出库日志
                    $all_inster[$go_db]['location_open_log'][] = [
                        'location_id' => $location_2['location_id'], //虚拟仓ID
                        'bar_code'    => $location_2['bar_code'], //商品条码
                        'type'        => 1, //1减少库存，2增加库存
                        'nums'        => $current_deduct, //商品数量
                        'available'   => 1, //是否操作可用库存：0否，1是
                        'actual'      => 1, //是否操作实际库存：0否，1是
                        'open_desc'   => "【次品】迁移库存至【{$company_name[$company_id]}】,商品可用和实际库存数量: {$current_deduct}", //操作说明
                        'create_time' => $now, //新增时间
                        'admin_name'  => '系统', //操作人名称
                        'admin_id'    => 0, //操作人id
                    ];
                    //新库入库日志
                    $all_inster[$company_id]['location_open_log'][] = [
                        'location_id' => $location_2['location_id'], //虚拟仓ID
                        'bar_code'    => $location_2['bar_code'], //商品条码
                        'type'        => 2, //1减少库存，2增加库存
                        'nums'        => $current_deduct, //商品数量
                        'available'   => 1, //是否操作可用库存：0否，1是
                        'actual'      => 1, //是否操作实际库存：0否，1是
                        'open_desc'   => "【{$company_name[$go_db]}】迁移【次品】库存至本库,商品可用和实际库存数量: {$current_deduct}", //操作说明
                        'create_time' => $now, //新增时间
                        'admin_name'  => '系统', //操作人名称
                        'admin_id'    => 0, //操作人id
                    ];
                    //新库入库

                    if (empty($all_inster[$company_id]['location_goods'][$location_2['seat_location_id']])) {
                        $in_data                    = $location_2;
                        $in_data['available_count'] = $current_deduct;
                        $in_data['actual_count']    = $current_deduct;
                    } else {
                        $in_data                    = $all_inster[$company_id]['location_goods'][$location_2['seat_location_id']];
                        $in_data['available_count'] = $in_data['available_count'] + $current_deduct;
                        $in_data['actual_count']    = $in_data['actual_count'] + $current_deduct;
                    }

                    $all_inster[$company_id]['location_goods'][$location_2['seat_location_id']] = $in_data;

                    //源库出库
                    $location_2['available_count']                 = $location_2['available_count'] - $current_deduct;
                    $location_2['actual_count']                    = $location_2['actual_count'] - $current_deduct;
                    $up_locations[$location_2['seat_location_id']] = $location_2;
                    $fake_count                                    = $fake_count - $current_deduct;
                }
            }
            $location_goods_group[$fictitious_good['bar_code']][1] = $location_items1;
            $location_goods_group[$fictitious_good['bar_code']][2] = $location_items2;

            //数据处理
            $fictitious_goods[$fictitious_good_key]['to_company_id'] = $company_id; //迁移的公司ID

            //去库存 log
            $all_inster[$go_db]['fictitious_open_log'][] = [
                'fictitious_id' => $fictitious_good['fictitious_id'], //虚拟仓ID
                'bar_code'      => $fictitious_good['bar_code'], //商品条码
                'type'          => 1, //1减少库存，2增加库存
                'nums'          => $good_fake_count, //商品数量
                'open_desc'     => "迁移库存至【{$company_name[$company_id]}】,商品总数量: {$fictitious_good['goods_count']},次品总数量: {$fictitious_good['fake_count']}", //操作说明
                'create_time'   => $now, //新增时间
                'admin_name'    => '系统', //操作人名称
                'admin_id'      => 0, //操作人id
            ];
            //增库存 log
            $all_inster[$company_id]['fictitious_open_log'][] = [
                'fictitious_id' => $fictitious_good['fictitious_id'], //虚拟仓ID
                'bar_code'      => $fictitious_good['bar_code'], //商品条码
                'type'          => 2, //1减少库存，2增加库存
                'nums'          => $good_fake_count, //商品数量
                'open_desc'     => "【{$company_name[$go_db]}】迁移库存至本仓库,商品总数量: {$fictitious_good['goods_count']},次品总数量: {$fictitious_good['fake_count']}", //操作说明
                'create_time'   => $now, //新增时间
                'admin_name'    => '系统', //操作人名称
                'admin_id'      => 0, //操作人id
            ];
            //增库存数据
            $all_inster[$company_id]['fictitious_goods'][] = $fictitious_good;
            //修改源数据库存
            $up_ids[] = $fictitious_good['fic_goods_id'];
        }

        #region 修改数据的组装
        $all_update[$go_db]['fictitious_goods'][] = [
            'where' => [
                ['fic_goods_id', 'in', $up_ids]
            ],
            'data'  => [
                'goods_count' => 0,
                'fake_count'  => 0,
            ]
        ];
        $locations_2                              = [];
        foreach ($up_locations as $location_i) {
            $locations_2["{$location_i['available_count']}_{$location_i['actual_count']}"][] = $location_i['seat_location_id'];
        }

        foreach ($locations_2 as $l2_k => $l2_ids) {
            list($l2_available_count, $l2_actual_count) = explode('_', $l2_k);
            $all_update[$go_db]['location_goods'][] = [
                'where' => [
                    ['seat_location_id', 'in', $l2_ids]
                ],
                'data'  => [
                    'available_count' => $l2_available_count,
                    'actual_count'    => $l2_actual_count,
                ]
            ];;
        }
        #endregion 修改数据的组装
        #endregion 所有需要移动的虚拟仓


        if ($param['show']) {
            return $this->success(compact('all_update', 'all_inster'));
        }

        foreach ($all_inster as $insert_db => $insert_tables) {
            foreach ($insert_tables as $insert_table => $insert_datas) {
                Db::connect($db[$insert_db])->name($insert_table)->insertAll($insert_datas);
            }
        }

        //执行数据库更新插入操作
        foreach ($all_update as $mdify_db => $modify_tables) {
            foreach ($modify_tables as $modify_table => $modify_datas) {
                foreach ($modify_datas as $modify_data) {
                    $res = Db::connect($db[$mdify_db])->name($modify_table)->where($modify_data['where'])->update($modify_data['data']);
                }
            }
        }
        return 'success';
    }


    public function exportSeparationDataBack($param)
    {
        $errors = [];
        $corps_count = [];
        $env   = 'prod'; //dev (测试环境)  or  prod (正式环境)
        $go_db = 1;//云酒
        $to_db = [2, 3, 4]; //科技 松鸽 木兰朵
        $db    = [
            '1' => "{$env}_mysql_wms_stock_1", //云酒
            '2' => "{$env}_mysql_wms_stock_2", //科技
            '3' => "{$env}_mysql_wms_stock_3", //松鸽
            '4' => "{$env}_mysql_wms_stock_4", //木兰朵
        ]; //数据库名 云酒 科技 松鸽 木兰朵


        $fictitious_company = [
            "佰酿云酒（南通常货仓）" => 5,
            "佰酿云酒（代发仓）"     => 5,
            "南通破损仓"           => 5,
            "南通次品仓"           => 5,
            "佰酿云酒（南通食品仓）" => 5,
            "供应商赠品仓"         => 5,


//            "云酒"                   => 1,
//            "佰酿云酒（南通闪购仓）"   => 1,
//            "佰酿云酒（南通淘宝）"     => 2,
//            "佰酿云酒（南通酒云京东）" => 2,
//            "佰酿云酒（南通拼多多）"   => 1,
//            "视频电商仓"             => 2,
//            "佰酿云酒（南通法南京东）" => 1,
//            "佰酿云酒（南通法南天猫）" => 1,
//            "佰酿云酒（天猫旗舰店）"   => 2,
//            "佰酿云酒（南通秒发仓）"   => 2,
//            "重庆古丝缇跨境仓"       => 6,
//            "保税区直发仓"           => 6,
//            "南通OT仓"               => 2,
//            "新媒体-南通仓"          => 2,
//            "广州南沙跨境仓"         => 6,
//            "佰酿云酒（南通猫超）"     => 1,
//            "佰酿云酒（BD仓）"         => 1,
//            "桃公子-南通仓"          => 2,
//            "渠道连锁店-南通仓"      => 2,
//            "佰酿云酒（重庆仓）"       => 1,
//            "微店-酒云旗舰店"        => 1,
//            "布拉德国际-南通仓"      => 1,
//            "佰酿云酒（科技）南通仓"   => 2,
//            "双系统切换盘点专用仓"   => 6,
//            "欧美保代管仓（南通）"     => 6,
//            "兔子窝"                 => 6,
//            "兔子窝01"               => 6,
//            "兔子窝残损仓"           => 6,
//            "存疑库存"               => 1,
//            "木兰朵"                 => 4,
//            "木兰朵（南通仓）"         => 4,
//            "酝享"                   => 6,
//            "酝享仓"                 => 6,
//            "佰酿云酒（天猫国际）"     => 1,
//            "次品待入仓"             => 1,
//            "布拉德国际-南通"        => 1,
//            "兔头商店-南通仓"        => 1,
//            "老外买酒-南通仓"        => 2,
//            "美酒线上仓"             => 1,
//            "美酒线上南通仓"         => 1,
//            "跨境退货仓"             => 6,
//            "拓展事业部"             => 1,
//            "佰酿云酒（拍卖仓）"       => 2,
//            "佰酿云酒（拍卖代管仓）"   => 6,
//            "酒云网抖音旗舰店仓"     => 1,
//            "酒云网淘宝拍卖店仓"     => 2,
//            "云酒科技（南通猫超）"     => 2,
//            "快团团南通仓"           => 1,
//            "松鸽酒业"               => 3,
//            "松鸽酒业南通仓"         => 3,
//            "云酒科技（链多多南通仓）" => 2,
//            "小红书-南通仓"          => 2,
//            "云酒科技"               => 2,
//            "云酒科技（拼多多南通仓）" => 2,
        ]; //仓库对应公司
        $company_name       = ['1' => '云酒', '2' => '科技', '3' => '松鸽', '4' => '木兰朵', '5' => '云酒科技',];

        $fictitious         = Db::connect($db[$go_db])->name('fictitious')
            ->where('fictitious_name', 'in', array_keys($fictitious_company))
            ->column('fictitious_name', 'fictitious_id');


        #region 入库数据查询
        $storages      = Db::connect($db[$go_db])->name('storage')
            ->where('status', 3) //入库单状态（1未开始，2清点上架中，3已完成，4已终止（不能再次启用
            ->where('fictitious_id', 'in', array_keys($fictitious))
            ->whereTime('create_time', '>=', "2023-01-01 00:00:00")
            ->column("storage_id,rd_code,fictitious_id,order_type");
        $storage_goods = Db::connect($db[$go_db])->name('storage_goods')
            ->where('storage_id', 'in', array_column($storages, 'storage_id'))
            ->column("storage_goods_id,storage_id,goods_name,bar_code");

        $storage_up_task_goods       = Db::connect($db[$go_db])->name('storage_up_task_goods')
            ->where('storage_goods_id', 'in', array_column($storage_goods, 'storage_goods_id'))
            ->column('storage_goods_id,good_number,bad_number');
        $storage_up_task_goods_group = [];
        foreach ($storage_up_task_goods as $storage_up_task_good_item) {

            $storage_up_task_goods_group[$storage_up_task_good_item['storage_goods_id']][] = $storage_up_task_good_item;
        }

        $storage_goods_group = [];
        foreach ($storage_goods as $storage_goods_item) {
            $in_stock_list = $storage_up_task_goods_group[$storage_goods_item['storage_goods_id']] ?? [];


            $storage_goods_item['in_good_number'] = array_sum(array_column($in_stock_list, 'good_number'));
            $storage_goods_item['in_bad_number']  = array_sum(array_column($in_stock_list, 'bad_number'));
            //$storage_goods_item['in_stock_list']  = $in_stock_list;

            $storage_goods_group[$storage_goods_item['storage_id']][] = $storage_goods_item;
        }

        $in_bar_codes = [];

        $rd_codes = [];
        foreach ($storages as $storage_1) {
            if (in_array($storage_1['order_type'], [1])) {//订单类型（1入库单，2调拨单）',
                $rd_codes[] = $storage_1['rd_code'];
            }
        }

        print_r($rd_codes);die ;


        if($env === 'dev'){
            $corp = 2;
        }else{
            $rd_code_corp = \Curl::getCorpbyCode(['codes'=>$rd_codes]);
        }

        foreach ($storages as $i => $storage) {
            //查询是公司 酒云 还是 科技的单子
            if($env !== 'dev'){
                if($storage['order_type'] == 2){//订单类型（1入库单，2调拨单）', //调拨单全是云酒
                    $corp = 1;
                }else{
                    $corps = $rd_code_corp[$storage['rd_code']];

                    if (count($corps) === 1) {
                        if (in_array('001', $corps)) {
                            $corp = 2; //科技
                        } else {
                            $corp = 1; // 云酒
                        }
                    }else{
                        $errors[$storage['rd_code']] = $corps;
//                    $corp = 1; // 云酒 ??  todo .........
                    }
                }
            }

            $corps_count[$corp][] = [$storage['rd_code'],$storage['fictitious_id'],];

            $goods_list = $storage_goods_group[$storage['storage_id']] ?? [];
            foreach ($goods_list as $goods_item) {
                $in_goods_origin_nums                                                                      = $in_bar_codes[$storage['fictitious_id']][$goods_item['bar_code']][$corp]['in_good_number'] ?? 0;
                $in_bar_codes[$storage['fictitious_id']][$goods_item['bar_code']][$corp]['in_good_number'] = $goods_item['in_good_number'] + +$in_goods_origin_nums;

                $in_bad_origin_nums                                                                       = $in_bar_codes[$storage['fictitious_id']][$goods_item['bar_code']][$corp]['in_bad_number'] ?? 0;
                $in_bar_codes[$storage['fictitious_id']][$goods_item['bar_code']][$corp]['in_bad_number'] = $goods_item['in_bad_number'] + $in_bad_origin_nums;
            }
//            $storages[$i]          = $storage;
        }


        #endregion 入库数据查询

        #region 出库数据查询
        $ship_order = Db::connect($db[$go_db])->name('ship_order')
            ->where([
                ['is_cancel_order', '=', 0], //撤销状态：0正常，1撤单成功，3撤单中，4撤单失败，5已合并
                ['fictitious_id', 'in', array_keys($fictitious)], //虚拟仓库二级id
            ])->column('id,fictitious_id');

        $ship_order_goods      = Db::connect($db[$go_db])->name('ship_order_goods')
            ->where([
                ['order_id', 'in', array_column($ship_order, 'id')], //虚拟仓库二级id
                ['fictitious_id', 'in', array_keys($fictitious)], //虚拟仓库二级id
            ])->column('id,order_id,bar_code,goods_id,nums');
        $ship_order_goods_grop = [];
        foreach ($ship_order_goods as $ship_order_good) {
            $ship_order_goods_grop[$ship_order_good['order_id']][] = $ship_order_good;
        }
        $fictitious_out_bar_codes = [];
        foreach ($ship_order as $ship_order_item) {
            $goods_list = $ship_order_goods_grop[$ship_order_item['id']] ?? [];

            foreach ($goods_list as $in_goods) {
                $origin_nums                                                                        = $fictitious_out_bar_codes[$ship_order_item['fictitious_id']][$in_goods['bar_code']] ?? 0;
                $fictitious_out_bar_codes[$ship_order_item['fictitious_id']][$in_goods['bar_code']] = $origin_nums + $in_goods['nums'];
            }
        }
        #endregion 入库数据查询

        $fictitious_goods2 = Db::connect($db[$go_db])->name('fictitious_goods')
            ->where('fictitious_id', 'in', array_keys($fictitious))
            ->column('*');

        $location_goods_group = [];
        $bar_codes            = array_column($fictitious_goods2, 'bar_code');// 全部条码
        $location_goods       = Db::connect($db[$go_db])->name('location_goods')
            ->where('bar_code', 'in', $bar_codes)
            ->column('*');
        foreach ($location_goods as $location_good) {
            $location_goods_group[$location_good['bar_code']][] = $location_good;
        }

        $location_nums = [];
        $fictitious_goods2_group = [];
        foreach ($fictitious_goods2 as $fictitious_good_key => $fictitious_good) {
            $temp_location = $location_goods_group[$fictitious_good['bar_code']] ?? [];
            $location_nums[$fictitious_good['fictitious_id']][$fictitious_good['bar_code']] = [
                'available_count' => array_sum(array_column($temp_location,'available_count')),
                'actual_count' => array_sum(array_column($temp_location,'actual_count')),
            ];
            $fictitious_goods2_group[$fictitious_good['fictitious_id']][$fictitious_good['bar_code']] = $fictitious_good;
        }


        $ret_datas  = [];
        $ret_header = [
            '仓库ID',
            '仓库名称',
            '所属公司',
            '入库数量(酒云)',
            '入库数量(酒云良品)',
            '入库数量(科技)',
            '入库数量(科技良品)',
            '出库数量',
            '库位可用数量',
            '库位实际数量',
            '虚拟仓商品总数量',
            '虚拟仓次品总数量',
            '产品条码',
        ];

        #region 組裝數據
        foreach ($fictitious as $f_id => $f_name) {
            $in_arr  = $in_bar_codes[$f_id] ?? [];
            $out_arr = $fictitious_out_bar_codes[$f_id] ?? [];
            $location_arr = $location_nums[$f_id] ?? [];
            $fake_count_arr = $fictitious_goods2_group[$f_id] ?? [];

            if (empty($in_arr) && empty($out_arr)) {
                $ret_datas[] = [
                    'wh_id'         => $f_id,
                    'wh_name'       => $f_name,
                    'company_name'  => $company_name[$fictitious_company[$f_name]],
                    'in_nums1_good' => 0,
                    'in_nums1_bad'  => 0,
                    'in_nums2_good' => 0,
                    'in_nums2_bad'  => 0,
                    'out_nums'      => 0,
                    'available_count' => 0,
                    'actual_count'  => 0,
                    'goods_count' => 0,
                    'fake_count'  => 0,
                    'bar_code'      => "",
                ];
            } else {
                $f_temps = [];
                foreach ($in_arr as $in_bar_code => $in_item) {
                    if (empty($f_temps[$in_bar_code])) {
                        $f_temps[$in_bar_code] = [
                            'wh_id'         => $f_id,
                            'wh_name'       => $f_name,
                            'company_name'  => $company_name[$fictitious_company[$f_name]],
                            'in_nums1_good' => 0,
                            'in_nums1_bad'  => 0,
                            'in_nums2_good' => 0,
                            'in_nums2_bad'  => 0,
                            'out_nums'      => 0,
                            'available_count' => 0,
                            'actual_count'  => 0,
                            'goods_count' => 0,
                            'fake_count'  => 0,
                            'bar_code'      => $in_bar_code,
                        ];
                    }

                    $f_temps[$in_bar_code]['in_nums1_good'] = $f_temps[$in_bar_code]['in_nums1_good'] + ($in_item[1]['in_good_number'] ?? 0);
                    $f_temps[$in_bar_code]['in_nums1_bad']  = $f_temps[$in_bar_code]['in_nums1_bad'] + ($in_item[1]['in_bad_number'] ?? 0);

                    $f_temps[$in_bar_code]['in_nums2_good'] = $f_temps[$in_bar_code]['in_nums2_good'] + ($in_item[2]['in_good_number'] ?? 0);
                    $f_temps[$in_bar_code]['in_nums2_bad']  = $f_temps[$in_bar_code]['in_nums2_bad'] + ($in_item[2]['in_bad_number'] ?? 0);
                }

                foreach ($out_arr as $out_bar_code => $out_num) {
                    if (empty($f_temps[$out_bar_code])) {
                        $f_temps[$out_bar_code] = [
                            'wh_id'         => $f_id,
                            'wh_name'       => $f_name,
                            'company_name'  => $company_name[$fictitious_company[$f_name]],
                            'in_nums1_good' => 0,
                            'in_nums1_bad'  => 0,
                            'in_nums2_good' => 0,
                            'in_nums2_bad'  => 0,
                            'out_nums'      => 0,
                            'available_count' => 0,
                            'actual_count'  => 0,
                            'goods_count' => 0,
                            'fake_count'  => 0,
                            'bar_code'      => $out_bar_code,
                        ];
                    }

                    $f_temps[$out_bar_code]['out_nums'] = $f_temps[$out_bar_code]['out_nums'] + $out_num;
                }

                foreach ($location_arr as $l_bar_code => $l_num) {
                    if (empty($f_temps[$l_bar_code])) {
                        $f_temps[$l_bar_code] = [
                            'wh_id'         => $f_id,
                            'wh_name'       => $f_name,
                            'company_name'  => $company_name[$fictitious_company[$f_name]],
                            'in_nums1_good' => 0,
                            'in_nums1_bad'  => 0,
                            'in_nums2_good' => 0,
                            'in_nums2_bad'  => 0,
                            'out_nums'      => 0,
                            'available_count' => 0,
                            'actual_count'  => 0,
                            'goods_count' => 0,
                            'fake_count'  => 0,
                            'bar_code'      => $l_bar_code,
                        ];
                    }

                    $f_temps[$l_bar_code]['available_count'] = $f_temps[$l_bar_code]['available_count'] + ($l_num['available_count'] ?? 0);

                    $f_temps[$l_bar_code]['actual_count'] = $f_temps[$l_bar_code]['actual_count'] + ($l_num['actual_count'] ?? 0);
                }

                foreach ($fake_count_arr as $g_bar_code => $g_num) {
                    if (empty($f_temps[$g_bar_code])) {
                        $f_temps[$g_bar_code] = [
                            'wh_id'           => $f_id,
                            'wh_name'         => $f_name,
                            'company_name'    => $company_name[$fictitious_company[$f_name]],
                            'in_nums1_good'   => 0,
                            'in_nums1_bad'    => 0,
                            'in_nums2_good'   => 0,
                            'in_nums2_bad'    => 0,
                            'out_nums'        => 0,
                            'available_count' => 0,
                            'actual_count'    => 0,
                            'goods_count' => 0,
                            'fake_count'  => 0,
                            'bar_code'        => $g_bar_code,
                        ];
                    }

                    $f_temps[$g_bar_code]['goods_count'] = ($g_num['goods_count'] ?? 0);

                    $f_temps[$g_bar_code]['fake_count'] = ($g_num['fake_count'] ?? 0);
                }

                foreach (array_values($f_temps) as $ret_item) {
                    $ret_datas[] = $ret_item;
                }
            }

        }
        #endregion 組裝數據




        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName('仓库数据.xls', '仓库数据1');


        $export_data = [];
        foreach ($ret_datas as $ret_data){
            $ret_data['bar_code'] = strval($ret_data['bar_code']);
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($ret_header)
            ->data($export_data)
            ->output();


        print_r($corps_count);
        print_r($filePath);
        print_r(PHP_EOL);
        print_r(PHP_EOL);
        print_r($errors);
        die;


        return $this->success([$storages]);
    }


    public function exportSeparationDataCompany($param)
    {
        $errors = [];
        $corps_count = [];
        $env   = 'prod'; //dev (测试环境)  or  prod (正式环境)
        $go_db = 1;//云酒
        $to_db = [2, 3, 4]; //科技 松鸽 木兰朵
        $db    = [
            '1' => "{$env}_mysql_wms_stock_1", //云酒
            '2' => "{$env}_mysql_wms_stock_2", //科技
            '3' => "{$env}_mysql_wms_stock_3", //松鸽
            '4' => "{$env}_mysql_wms_stock_4", //木兰朵
        ]; //数据库名 云酒 科技 松鸽 木兰朵


        $fictitious_company = [
            "佰酿云酒（南通常货仓）" => 5,
            "佰酿云酒（代发仓）"     => 5,
            "南通破损仓"           => 5,
            "南通次品仓"           => 5,
            "佰酿云酒（南通食品仓）" => 5,
            "供应商赠品仓"         => 5,
        ]; //仓库对应公司
        $company_name       = ['1' => '云酒', '2' => '科技', '3' => '松鸽', '4' => '木兰朵', '5' => '云酒科技',];

        $fictitious         = Db::connect($db[$go_db])->name('fictitious')
            ->where('fictitious_name', 'in', array_keys($fictitious_company))
            ->column('fictitious_name', 'fictitious_id');


        #region 入库数据查询
        $storages      = Db::connect($db[$go_db])->name('storage')
            ->where('status', 3) //入库单状态（1未开始，2清点上架中，3已完成，4已终止（不能再次启用
            ->where('fictitious_id', 'in', array_keys($fictitious))->column("storage_id,rd_code,fictitious_id,order_type");

        $rd_codes = [];
        foreach ($storages as $storage_1) {
            if (in_array($storage_1['order_type'], [1])) {//订单类型（1入库单，2调拨单）',  调拨单全是云酒
                $rd_codes[] = $storage_1['rd_code'];
            }
        }

        $rd_code_corp = \Curl::getCorpbyCode(['codes'=>$rd_codes]);

        $header = ['仓库',"storage_id", "入库单号", "调拨单号", "单子所属公司", "单子所属公司代码", ];
        $data = [];


        foreach ($storages as $i => $storage) {
            $item = [
                'wh'           => $fictitious[$storage['fictitious_id']],
                'storage_id'   => $storage['storage_id'],
                'rd_code1'     => in_array($storage['order_type'], [1]) ? $storage['rd_code'] : '',
                'rd_code2'     => in_array($storage['order_type'], [2]) ? $storage['rd_code'] : '',
                'corp'         => '',
                'company_code' => '',
            ];

            //查询是公司 酒云 还是 科技的单子
                if(in_array($storage['order_type'],[2])){//订单类型（1入库单，2调拨单）', //调拨单全是云酒
                    $corp = '云酒';
                    $company_code = '调拨单全是云酒';
                }else{
                    $corps        = $rd_code_corp[$storage['rd_code']];
                    $company_code = implode(',', $corps);
                    if (count($corps) === 1) {
                        if (in_array('001', $corps)) {
                            $corp = '科技'; //科技
                        } else {
                            $corp = '云酒'; // 云酒
                        }
                    }else{
                        $errors[$storage['rd_code']] = $corps;
                        $corp = "多个混合";
                    }
                }

            $item['corp'] = $corp;
            $item['company_code'] = $company_code;

            $data[] = $item;
        }




        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName('仓库调拨入库单数据.xls', '仓库调拨入库单数据');


        $export_data = [];
        foreach ($data as $ret_data){
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();


        print_r($errors);
        die;
    }

    public function exportSeparationData($param)
    {
        $fic_info_list = []; //仓库和公司信息列表
        $fic_ids       = []; //全部仓库ID
        $env           = 'prod'; //dev (测试环境)  or  prod (正式环境)

        #region 数据初始化和处理
        $kj_start_time      = strtotime('2023-01-01 00:00:00');
        $errors             = [];
        $corps_count        = [];
        $go_db              = 1;//云酒
        $to_db              = [2, 3, 4]; //科技 松鸽 木兰朵
        $db                 = [
            '1' => "{$env}_mysql_wms_stock_1", //云酒
            '2' => "{$env}_mysql_wms_stock_2", //科技
            '3' => "{$env}_mysql_wms_stock_3", //松鸽
            '4' => "{$env}_mysql_wms_stock_4", //木兰朵
        ]; //数据库名 云酒 科技 松鸽 木兰朵
        $fictitious_company = [
            "佰酿云酒（南通常货仓）" => 5,
            "佰酿云酒（代发仓）"     => 5,
            "南通破损仓"           => 5,
            "南通次品仓"           => 5,
            "佰酿云酒（南通食品仓）" => 5,
            "供应商赠品仓"         => 5,
        ]; //仓库对应公司
        $company_name       = ['1' => '云酒', '2' => '科技', '3' => '松鸽', '4' => '木兰朵', '5' => '云酒科技',];

        $fictitious = Db::connect($db[$go_db])->name('fictitious')
            ->where('fictitious_name', 'in', array_keys($fictitious_company))
            ->column('fictitious_name', 'fictitious_id');
        foreach ($fictitious as $id => $fic_name) {
            $fic_info_list[$id] = [
                'id'           => $id,
                'name'         => $fic_name,
                'company_code' => $fictitious_company[$fic_name],
                'company_name' => $company_name[$fictitious_company[$fic_name]],
            ];
            $fic_ids[]          = $id;
        }
        #endregion 数据初始化和处理

        #region 入库数据查询

        #region 入库单数据处理
        $storages      = Db::connect($db[$go_db])->name('storage')
            ->where('status', 3) //入库单状态（1未开始，2清点上架中，3已完成，4已终止（不能再次启用)
            ->where('fictitious_id', 'in', $fic_ids) // 指定的这几个仓库
            ->column("storage_id,rd_code,fictitious_id,order_type,create_time");
        $storage_goods = Db::connect($db[$go_db])->name('storage_goods')
            ->where('storage_id', 'in', array_column($storages, 'storage_id')) //调拨入库单ID
            ->column("storage_goods_id,storage_id,bar_code");

        $storage_up_task_goods = Db::connect($db[$go_db])->name('storage_up_task_goods')
            ->where('storage_goods_id', 'in', array_column($storage_goods, 'storage_goods_id'))
            ->column('storage_goods_id,good_number,bad_number');

        $storage_up_task_goods_group = [];
        foreach ($storage_up_task_goods as $sub_var1) {
            $storage_up_task_goods_group[$sub_var1['storage_goods_id']][] = $sub_var1;
        }

        $storage_goods_group = $storage_goods_group_temp = [];
        foreach ($storage_goods as $storage_goods_item) {
            $in_stock_list = $storage_up_task_goods_group[$storage_goods_item['storage_goods_id']] ?? [];

            $storage_goods_item['in_good_number'] = array_sum(array_column($in_stock_list, 'good_number'));
            $storage_goods_item['in_bad_number']  = array_sum(array_column($in_stock_list, 'bad_number'));

            $storage_goods_group_temp[$storage_goods_item['storage_id']][] = $storage_goods_item;
        }

        foreach ($storage_goods_group_temp as $storage_id => $storage_goods_items) {
            foreach ($storage_goods_items as $storage_goods_item) {
                $storage_goods_group[$storage_id][$storage_goods_item['bar_code']][] = $storage_goods_item;
            }
        }
        #endregion 入库单数据处理

        #region 获取每个单子的公司
        $rd_codes = [];
        foreach ($storages as $storage_1) {
            //2023年之前的单子全是酒云的.
            if (in_array($storage_1['order_type'], [1]) && ($storage_1['create_time'] >= $kj_start_time)) {//订单类型（1入库单，2调拨单）',
                $rd_codes[] = $storage_1['rd_code']; //只有入库单才查询 并且2023年之后的才查询是否科技
            }
        }

        if ($env === 'dev') {
            $corp = 2;
        } else {
            $rd_code_corp = \Curl::getCorpbyCode(['codes' => $rd_codes]); //查询全部单子
        }
        #endregion 获取每个单子的公司

        $in_bar_codes = [];
        #region 数据处理
        //region 查询是公司 酒云 还是 科技的单子 过滤掉其他的
        $temp_storages = [];
        foreach ($storages as $storage) {
            if ($env !== 'dev') {
                if ($storage['order_type'] == 2) {//订单类型（1入库单，2调拨单）',
                    $corp = 1; //调拨单全是云酒
                } else {
                    if ($storage_1['create_time'] >= $kj_start_time) {
                        $corps = $rd_code_corp[$storage['rd_code']] ?? [];

                        if (count($corps) === 1) {
                            if (in_array('001', $corps)) {
                                $corp = 2; //科技
                            } else {
                                $corp = 1; // 云酒
                            }
                        } else {
                            $errors[$storage['rd_code']] = $corps;

                            if ((count($corps) > 0) && in_array('001', $corps)) {
                                $corp = 2; //科技
                            } else {
                                $corp = 3; // 其他
                            }
                        }
                    } else {
                        $corp = 1; //2023年之前全是云酒
                    }
                }
            }
            $storage['corp']      = $corp;
            $storage['corp_code'] = implode(',', $corps ?? []);

            if (in_array($corp, [1, 2])) {
                $temp_storages[] = $storage;
            }
        }
        #endregion 查询是公司 酒云 还是 科技的单子

        foreach ($temp_storages as $storage) {
            $corp                 = $storage['corp'];
            $corps_count[$corp][] = $storage['storage_id'];

            $goods_list = $storage_goods_group[$storage['storage_id']] ?? [];
            $s_fic_id   = $storage['fictitious_id']; //仓库ID

            foreach ($goods_list as $gi_bar_code => $goods_bar_list) {
                $origin_in_good_number = $in_bar_codes[$s_fic_id][$gi_bar_code][$corp]['in_good_number'] ?? 0;
                $origin_in_bad_number  = $in_bar_codes[$s_fic_id][$gi_bar_code][$corp]['in_bad_number'] ?? 0;

                $in_bar_codes[$s_fic_id][$gi_bar_code][$corp]['in_good_number'] = $origin_in_good_number + array_sum(array_column($goods_bar_list, 'in_good_number'));
                $in_bar_codes[$s_fic_id][$gi_bar_code][$corp]['in_bad_number']  = $origin_in_bad_number + array_sum(array_column($goods_bar_list, 'in_bad_number'));
            }
        }
        if ($env == 'dev') {
            print_r('$in_bar_codes' . PHP_EOL);
            print_r($in_bar_codes);
            print_r(PHP_EOL);
        }
        #endregion
        #endregion 入库数据查询

        #region 出库数据查询
        $ship_order = Db::connect($db[$go_db])->name('ship_order')
            ->where([
                ['is_cancel_order', '=', 0], //撤销状态：0正常，1撤单成功，3撤单中，4撤单失败，5已合并
                ['fictitious_id', 'in', $fic_ids], //虚拟仓库二级id
            ])->column('id,fictitious_id');

        $ship_order_goods = Db::connect($db[$go_db])->name('ship_order_goods')
            ->where([
                ['order_id', 'in', array_column($ship_order, 'id')], //虚拟仓库二级id
//                ['fictitious_id', 'in', $fic_ids], //虚拟仓库二级id
            ])->column('id,order_id,bar_code,goods_id,nums');

        $ship_order_goods_group = [];
        foreach ($ship_order_goods as $ship_order_good) {
            $ship_order_goods_group[$ship_order_good['order_id']][] = $ship_order_good;
        }
        $fictitious_out_bar_codes = [];
        foreach ($ship_order as $ship_order_item) {
            $goods_list = $ship_order_goods_group[$ship_order_item['id']] ?? [];

            foreach ($goods_list as $out_goods) {
                $fictitious_out_bar_codes[$ship_order_item['fictitious_id']][$out_goods['bar_code']][] = $out_goods;
            }
        }

        $out_bar_codes = [];
        foreach ($fictitious_out_bar_codes as $out_fic_id => $bar_code_list) {
            foreach ($bar_code_list as $out_bar_code => $out_nums_list) {
                $out_bar_codes[$out_fic_id][$out_bar_code] = array_sum(array_column($out_nums_list, 'nums'));
            }
        }
        #endregion 入库数据查询

        #region 仓库库位数据查询
        $fictitious_goods2 = Db::connect($db[$go_db])->name('fictitious_goods')
            ->where('fictitious_id', 'in', array_keys($fictitious))
            ->column('fic_goods_id,fictitious_id,bar_code,goods_count,fake_count');

        $location_goods_group = [];
        $bar_codes            = array_column($fictitious_goods2, 'bar_code');// 全部条码
        $location_goods       = Db::connect($db[$go_db])->name('location_goods')
            ->where('bar_code', 'in', $bar_codes)
            ->column('seat_location_id,bar_code,location_id,available_count,actual_count');
        foreach ($location_goods as $location_good) {
            $location_goods_group[$location_good['bar_code']][] = $location_good;
        }

        $location_nums = [];
        foreach ($fictitious_goods2 as $fictitious_good_key => $fictitious_good) {
            $temp_fic_id   = $fictitious_good['fictitious_id'];
            $temp_bar_code = $fictitious_good['bar_code'];

            $temp_location                               = $location_goods_group[$temp_bar_code] ?? [];
            $location_nums[$temp_fic_id][$temp_bar_code] = [
                'available_count' => array_sum(array_column($temp_location, 'available_count')),
                'actual_count'    => array_sum(array_column($temp_location, 'actual_count')),
                'goods_count'     => $fictitious_good['goods_count'],
                'fake_count'      => $fictitious_good['fake_count'],
            ];
        }
        #endregion 仓库库位数据查询


        $ret_datas  = [];
        $ret_header = [
            '仓库ID',
            '仓库名称',
            '所属公司',
            '入库数量(酒云)',
            '入库数量(酒云良品)',
            '入库数量(科技)',
            '入库数量(科技良品)',
            '出库数量',
            '库位可用数量',
            '库位实际数量',
            '虚拟仓商品总数量',
            '虚拟仓次品总数量',
            '产品条码',
        ];

        #region 組裝數據
        foreach ($fictitious as $f_id => $f_name) {
            $in_arr        = $in_bar_codes[$f_id] ?? [];
            $out_arr       = $out_bar_codes[$f_id] ?? [];
            $location_arr  = $location_nums[$f_id] ?? [];
            $temp_item_arr = [
                'wh_id'           => $f_id,
                'wh_name'         => $f_name,
                'company_name'    => $company_name[$fictitious_company[$f_name]],
                'in_nums1_good'   => 0,
                'in_nums1_bad'    => 0,
                'in_nums2_good'   => 0,
                'in_nums2_bad'    => 0,
                'out_nums'        => 0,
                'available_count' => 0,
                'actual_count'    => 0,
                'goods_count'     => 0,
                'fake_count'      => 0,
                'bar_code'        => "",
            ];

            if (empty($in_arr) && empty($out_arr) && empty($location_arr)) {
                $ret_datas[] = $temp_item_arr;
            } else {
                $f_temps = [];


                foreach ($in_arr as $in_bar_code => $in_item) {
                    if (empty($f_temps[$in_bar_code])) {
                        $f_temps[$in_bar_code]             = $temp_item_arr;
                        $f_temps[$in_bar_code]['bar_code'] = $in_bar_code;
                    }

                    $f_temps[$in_bar_code]['in_nums1_good'] = $f_temps[$in_bar_code]['in_nums1_good'] + ($in_item[1]['in_good_number'] ?? 0);
                    $f_temps[$in_bar_code]['in_nums1_bad']  = $f_temps[$in_bar_code]['in_nums1_bad'] + ($in_item[1]['in_bad_number'] ?? 0);

                    $f_temps[$in_bar_code]['in_nums2_good'] = $f_temps[$in_bar_code]['in_nums2_good'] + ($in_item[2]['in_good_number'] ?? 0);
                    $f_temps[$in_bar_code]['in_nums2_bad']  = $f_temps[$in_bar_code]['in_nums2_bad'] + ($in_item[2]['in_bad_number'] ?? 0);
                }

                foreach ($out_arr as $out_bar_code => $out_num) {
                    if (empty($f_temps[$out_bar_code])) {
                        $f_temps[$out_bar_code]             = $temp_item_arr;
                        $f_temps[$out_bar_code]['bar_code'] = $out_bar_code;
                    }

                    $f_temps[$out_bar_code]['out_nums'] = $f_temps[$out_bar_code]['out_nums'] + $out_num;
                }

                foreach ($location_arr as $l_bar_code => $l_num) {
                    if (empty($f_temps[$l_bar_code])) {
                        $f_temps[$l_bar_code]             = $temp_item_arr;
                        $f_temps[$l_bar_code]['bar_code'] = $l_bar_code;
                    }

                    $f_temps[$l_bar_code]['available_count'] = $f_temps[$l_bar_code]['available_count'] + ($l_num['available_count'] ?? 0);

                    $f_temps[$l_bar_code]['actual_count'] = $f_temps[$l_bar_code]['actual_count'] + ($l_num['actual_count'] ?? 0);

                    $f_temps[$l_bar_code]['goods_count'] = $f_temps[$l_bar_code]['goods_count'] + ($l_num['goods_count'] ?? 0);
                    $f_temps[$l_bar_code]['fake_count']  = $f_temps[$l_bar_code]['fake_count'] + ($l_num['fake_count'] ?? 0);
                }

                foreach (array_values($f_temps) as $ret_item) {
                    $ret_item['bar_code'] = strval($ret_item['bar_code']);
                    $ret_datas[] = $ret_item;
                }
            }

        }
        #endregion 組裝數據


        $filePath = $this->exportExcel([
            'filename'   => '仓库数据.xls',
            'sheet_name' => '仓库数据1',
            'header'     => $ret_header,
            'data'       => $ret_datas,
        ]);


        print_r($corps_count);
        print_r($filePath);
        print_r(PHP_EOL);
        print_r(PHP_EOL);
        print_r($errors);
        die;


        return $this->success([$storages]);
    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            $export_data[] = array_values($ret_data);
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }


    public function userMatch($param)
    {
        $arr1 = [
            "341066"  => "/CzO53ZOTZK6lpPHHbu5jg==",
            "1102812" => "0Q0eaqSvjKXTnU/MBTTkkQ==",
            "247993"  => "1/xky3C59ggEjCJlcvtHwg==",
            "1022251" => "1HBWT4KUFxKIU4Hf1EGE2Q==",
            "811560"  => "2dlzPMrpZ7fL0Et4UH+n9A==",
            "356976"  => "3BBuNbSI2CpLUJYUqh7ozA==",
            "304756"  => "3DR0c/85r8JCuvw/wqVkRg==",
            "1101130" => "3JkrnKGmiz7RQd2Uzjg/lg==",
            "1103708" => "3jTYZzEIS9KUseAeqL2eIw==",
            "1062602" => "3jTYZzEIS9KUseAeqL2eIw==",
            "148856"  => "4BbQtBFv7Vb9OddTjwLM2w==",
            "793351"  => "4t9Pe6kIfIXwvWwqIguTdQ==",
            "1081071" => "4y+7D6KeQUX5pDVZNFb0XQ==",
            "1088928" => "6AEDy4aaqm56hGhIWQZDjg==",
            "740297"  => "6dMRNUQV9z/w1dWCDcfhmQ==",
            "1025501" => "6l2S7j9MO6aZRUmyCwZ6Rg==",
            "739042"  => "6Q3G8OmB1wnnoq7a3yyWwA==",
            "783968"  => "6YRd4Ihvy4ATJucp828jTQ==",
            "1103958" => "7+4UBSBOEIvok/+m9VVPLQ==",
            "1101128" => "7+4UBSBOEIvok/+m9VVPLQ==",
            "345138"  => "7BxSo6cjL2tHSQ0WgEzefg==",
            "326480"  => "7CaMK59oV05wQ4grw43t6w==",
            "353589"  => "7nWDsEqlgRxCyribHMiPqg==",
            "711648"  => "7T3Sc2yGXtf836ZjbiZqxQ==",
            "186592"  => "8puXV57KyaNtPkElxkQHpw==",
            "1100896" => "9CnmgKVBvR1uOfRyDRBigA==",
            "1000028" => "9SjRbwM1kZlCHfPQbo9GYQ==",
            "813281"  => "9SjRbwM1kZlCHfPQbo9GYQ==",
            "1000015" => "9SjRbwM1kZlCHfPQbo9GYQ==",
            "1103291" => "a3cemduGol3QCEQ5S2J/QQ==",
            "306506"  => "bAezJsfPFU2Vzq/YZvvckw==",
            "1078096" => "bchbbyy/qq/3i9ysL+F5Eg==",
            "234820"  => "bf8sDtiuIA10AIkBM5RzcQ==",
            "329409"  => "BgiK/82WkQq5I43XDTf8lA==",
            "327975"  => "BN6WRW1/LIbkfYa4glqL7g==",
            "1085170" => "bWs9RD94ULWjEDjjNdPG6A==",
            "341488"  => "CAZouUN7HsM0bM8T5OdJxA==",
            "304042"  => "CEGWhJDPdyeuUh77lrQZOg==",
            "1080878" => "D1jcXoZALadEMbo/gmCtSg==",
            "810830"  => "D1jcXoZALadEMbo/gmCtSg==",
            "347762"  => "dgBB9YNkLQa3U/BUWaL53Q==",
            "1031656" => "DKTos4fM3z7gTU+vmK30sA==",
            "245239"  => "dnTiesbCMR9AcIieUz19Pg==",
            "338772"  => "DQ9keLxXEjw/bHb/GlAhbQ==",
            "262349"  => "dvSwFgr68gAlapeAtkF8sQ==",
            "133225"  => "e59qE22Z2cemhFfLyqtCkA==",
            "152883"  => "e59qE22Z2cemhFfLyqtCkA==",
            "1076762" => "E8d5v+Oy6Y4ylPTt9ekMSg==",
            "344436"  => "EGnVh0Jx4JKQj7n/MYJaug==",
            "336999"  => "eOTl0UpQHJ3Os5W5OoXiKA==",
            "1038367" => "f1oxpeWrGOGS6S2Ir/xqmw==",
            "1003511" => "F4ybshBefkzotSgieQv+Hg==",
            "281182"  => "FDS8mhlX0weTeQhTKg0KNw==",
            "70132"   => "fNU8K675UM8HmDDrrwOf5A==",
            "65089"   => "fNU8K675UM8HmDDrrwOf5A==",
            "352924"  => "FpyyW1ZZfjXdnBKOo+bShg==",
            "279164"  => "FQqk4FSBSrDwiyIhsEmzLQ==",
            "99481"   => "FSUq/ZiR1DOuMNKDfjZBnw==",
            "144061"  => "fzrtWS/NaJmecMrIXpFu6g==",
            "245274"  => "G38sCGgnbNzLzy8B05h9zg==",
            "354374"  => "GDSwQcRvLOtYr1WGy/+wcA==",
            "804164"  => "GG6XaatD+0N47NzvNJC2Jg==",
            "1013733" => "Gu4wbFBfioqjrS/kon1Ukw==",
            "1104161" => "GxfvIYUrTtSmRc/AuMrCsw==",
            "1073159" => "HEbZ6X4yXKEIlIv5m1THFA==",
            "1029921" => "hLMYay+xsVyBZxIcMpFwHg==",
            "762423"  => "Hsgf0TtbJcgePTmeMvNnGQ==",
            "144802"  => "hsSU5pQDZ6uMzjOj3GIvEQ==",
            "180863"  => "I8JSSr8VCclXTvjlxys/bg==",
            "1070605" => "iEtulS4vkcIDX5raWV31fw==",
            "1075921" => "illHGrd7E156H8H0kg+yjw==",
            "215056"  => "itwOlGNPFmfnyk0H0hg6tw==",
            "166456"  => "j0fHIYKToIBPzbu/h+AcDQ==",
            "1103451" => "J82cNXKzWBnIBnI+uPQerg==",
            "799437"  => "J82cNXKzWBnIBnI+uPQerg==",
            "1080866" => "J82cNXKzWBnIBnI+uPQerg==",
            "1100904" => "J82cNXKzWBnIBnI+uPQerg==",
            "1011984" => "jQlLemebREreVqhGsNIfzg==",
            "742706"  => "jVzJZXE3JsPEmDRTdXW+dA==",
            "719176"  => "k/CwNfy38Mci97JkBw5rOA==",
            "1076394" => "k984+W2Xs2gLi1k2dvMmcA==",
            "343106"  => "khv1ThaZXxIMosNGMRdduA==",
            "768699"  => "kiwsMdwiVHlcGYW0olpruw==",
            "763191"  => "Kk2A8Sok545F/qjGxZPU1w==",
            "238316"  => "l+zO2Xalpuj6XcJrrDGV3Q==",
            "320715"  => "lNrkiQDkyS3iOZ7WvEYVvw==",
            "1004696" => "lO0s4csz1chhO0NwMTtuxw==",
            "109840"  => "lSe0eobEzlGplCgjKWJaKg==",
            "1038876" => "lvxN1KfjoCl1a+xAGeylJA==",
            "343813"  => "M3Hw4Y3adeb1rIl+1JX6aw==",
            "347012"  => "mej4qubb/jwSDuOUREHdKg==",
            "310373"  => "MgT7fQH9wQgy3yXGIs5bPg==",
            "314030"  => "mJU2UCOYaC33FEnv13R2ew==",
            "1091567" => "Mk4nvHG7S8Zdf3CtgV46xQ==",
            "354705"  => "mNkoo9QJ9VqA/CFUJELXHw==",
            "277322"  => "MXdsgjpzn47xrfY9rkx3zg==",
            "1000072" => "n2XVZw045ng4l6xEUTfaqA==",
            "1001317" => "N4fSm+gcJsOCqbVL20XeRQ==",
            "747107"  => "naetDpAcE4dXozOjaT2Nbg==",
            "346244"  => "NgFa0vFo/NioXpx4h3O6hg==",
            "812244"  => "nqulr2mD/R7PmarDUx/h/A==",
            "713453"  => "NV+vOXTz3N7VohIwqcmFww==",
            "157840"  => "ojR4btwdpNCmD+k7MyfoAw==",
            "1022244" => "oo9wSlT5xKxLt5jHHUEQqQ==",
            "339222"  => "oQXhsnpT5LJWHv5uIWfzew==",
            "1001049" => "ozhWYfHBrQ/Dx4y07/RlBg==",
            "1074233" => "p7R/3kBQdfknY6dk8GDPTg==",
            "343045"  => "pBkdvhV9ZKC5DEcuv4MkKA==",
            "240985"  => "PzCKsuEkxVijByiwJumSbQ==",
            "1073030" => "QCjZveKT1I5ZqVzKyK/riA==",
            "304461"  => "QO8EDqY5v5le//zr3xWqNA==",
            "1099240" => "qR1jeR1BsDgO1o81j1mung==",
            "1037910" => "R5bOSf75Zf322iTTN2MxyA==",
            "351043"  => "rgSg11JHIFC3t2zZ20uU8g==",
            "243825"  => "riKispvZ7Ml9ZGT0c5GNag==",
            "1103436" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1021400" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1094395" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1100743" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1100756" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1103431" => "rT7/pFICZ97WdVq6PV5D6g==",
            "1103435" => "rT7/pFICZ97WdVq6PV5D6g==",
            "707578"  => "siFkFO+iE2CAkkYIiDhoxw==",
            "171231"  => "sIT+L08PcXkQOYEB+dEpIQ==",
            "1104196" => "sYfvvFnIUBFUYwPI9Cz+hA==",
            "174285"  => "tCbcrM65cz08Ce7nxVoH0Q==",
            "245982"  => "u3EYdJ3VKPETcktCmn/oNg==",
            "1002098" => "UjN03AGZ7QAfwOhxGGC2sw==",
            "1104159" => "uS3IUaxEn0uwpsV8AX8gcQ==",
            "354042"  => "USud47KZKX0uMJ0UFqexxw==",
            "1076372" => "UzBLM/JKZQUxeB3cLa1NUg==",
            "804740"  => "uzr50/ehY1Vh8Pc0Fu5ssQ==",
            "783992"  => "v0Xa06lBaRxeF+CmxEA7BA==",
            "745213"  => "vbbdykH9L0elNB5IGA5u8Q==",
            "154417"  => "vf+kHltvSKEu0k0Z1FUsKg==",
            "1001996" => "viQLwvQrar7GILg0CXuXYw==",
            "1060049" => "VT2KaJbHHANHHvFOnbGOwA==",
            "334230"  => "W7oyPpC5NMJm9t56L99XfQ==",
            "317454"  => "w8LOJDyoB5BAZEHfIzYXLQ==",
            "328584"  => "WeAKt6kFKrJEgB7YY1Lkrw==",
            "1072795" => "WeSfj5jPDoFAICKXEu+sEA==",
            "160506"  => "wKYiE7H6qIvDnyXvn6L0pg==",
            "715568"  => "WLiM406Vp5LKKZX63SYpCw==",
            "1058720" => "Wul5q7T7CD9VvaEDt/Dc+Q==",
            "105423"  => "x+9DaCk3/mxnwv5XMzcnDA==",
            "172132"  => "X48RtgEk7iNKikafZqO31Q==",
            "331638"  => "x8YUeUUmqsn/CuK5sCPGhg==",
            "332569"  => "xb+l7h5isUXKwfb2P46b+g==",
            "240493"  => "XhSiiVgq7qdm7thixWQvRg==",
            "1102857" => "xq3pxg+bsSbMScZTlj6uEw==",
            "233759"  => "xq3pxg+bsSbMScZTlj6uEw==",
            "734445"  => "y6Qy1QEV+d464fjHLOJHQw==",
            "1088257" => "yAefNvBLP5nbd+JYsBPxLA==",
            "285790"  => "yBlj45F2Qq+Xfe0V2ypvSw==",
            "223129"  => "yCjKaropb39oBy+MYnQA4Q==",
            "1084795" => "yHkJEaOkcMJz9/JsECB/dw==",
            "1080004" => "Yi6eobV4VKthLSiTE3ItTA==",
            "343951"  => "yNLgn4t0RltkCQRPiAC7+g==",
            "213191"  => "yPM8SNnxQdzBmEQhBVqFtw==",
            "1100558" => "yQB3k9FNtzWkPvyBjbsnMA==",
            "128724"  => "YZrMUoGsS+unkHKlkIowoA==",
            "1002546" => "z889n6UNdqcrkvf4ufXILQ==",
            "1068792" => "ZAyg2PzDdzLzieg92C+kuw==",
            "1104135" => "ZAyg2PzDdzLzieg92C+kuw==",
            "235700"  => "ZBiAXUet33xq+sGZk9dcSA==",
            "323423"  => "zpCYBAQRWKxjfbiXybmivg==",
            "811942"  => "Zrmb6Lht73Di57CZzM1rCQ==",
            "94881"   => "zZlAmmXZ5ZZfKUJUR3VjjA==",
        ];

        $arr2 = [
            "mNkoo9QJ9VqA/CFUJELXHw==" => "13052888102",
            "zCXZ/Eebs1Ll2FvSn1ZbNA==" => "13055528237",
            "yPM8SNnxQdzBmEQhBVqFtw==" => "13114065934",
            "SQl5ZKivOS/QOnoDE/WqTg==" => "13167960434",
            "xq3pxg+bsSbMScZTlj6uEw==" => "13308059058",
            "XiWwWtKxoc4c9UlBXPUDdg==" => "13349862009",
            "zpCYBAQRWKxjfbiXybmivg==" => "13426130308",
            "M3Hw4Y3adeb1rIl+1JX6aw==" => "13452140083",
            "x+9DaCk3/mxnwv5XMzcnDA==" => "13482216046",
            "YZrMUoGsS+unkHKlkIowoA==" => "13482429017",
            "GxfvIYUrTtSmRc/AuMrCsw==" => "13508361427",
            "fzrtWS/NaJmecMrIXpFu6g==" => "13524939088",
            "ZBiAXUet33xq+sGZk9dcSA==" => "13527317787",
            "lSe0eobEzlGplCgjKWJaKg==" => "13527525741",
            "bf8sDtiuIA10AIkBM5RzcQ==" => "13533688073",
            "QO8EDqY5v5le//zr3xWqNA==" => "13594763089",
            "4BbQtBFv7Vb9OddTjwLM2w==" => "13608330352",
            "ojR4btwdpNCmD+k7MyfoAw==" => "13608345999",
            "Kk2A8Sok545F/qjGxZPU1w==" => "13617614047",
            "CEGWhJDPdyeuUh77lrQZOg==" => "13628218772",
            "3JkrnKGmiz7RQd2Uzjg/lg==" => "13637965600",
            "FpyyW1ZZfjXdnBKOo+bShg==" => "13650590404",
            "6dMRNUQV9z/w1dWCDcfhmQ==" => "13671678431",
            "6l2S7j9MO6aZRUmyCwZ6Rg==" => "13677647465",
            "zZlAmmXZ5ZZfKUJUR3VjjA==" => "13801966252",
            "IbV7PPtAd+3wicW70PHPFg==" => "13813631416",
            "HEbZ6X4yXKEIlIv5m1THFA==" => "13847663799",
            "R5bOSf75Zf322iTTN2MxyA==" => "13862800870",
            "2dlzPMrpZ7fL0Et4UH+n9A==" => "13883847221",
            "w8LOJDyoB5BAZEHfIzYXLQ==" => "13896002202",
            "khv1ThaZXxIMosNGMRdduA==" => "13896724521",
            "fNU8K675UM8HmDDrrwOf5A==" => "13901731030",
            "BgiK/82WkQq5I43XDTf8lA==" => "13912423290",
            "3jTYZzEIS9KUseAeqL2eIw==" => "13962852161",
            "yBlj45F2Qq+Xfe0V2ypvSw==" => "13983010313",
            "dvSwFgr68gAlapeAtkF8sQ==" => "13983021166",
            "f1oxpeWrGOGS6S2Ir/xqmw==" => "13983028475",
            "wKYiE7H6qIvDnyXvn6L0pg==" => "13983079595",
            "Zrmb6Lht73Di57CZzM1rCQ==" => "13983241864",
            "bAezJsfPFU2Vzq/YZvvckw==" => "13983664017",
            "1zC0fNWMejbsrEq05o8+vw==" => "13983888450",
            "Yi6eobV4VKthLSiTE3ItTA==" => "15019431962",
            "GG6XaatD+0N47NzvNJC2Jg==" => "15021159859",
            "l+zO2Xalpuj6XcJrrDGV3Q==" => "15023787902",
            "/CzO53ZOTZK6lpPHHbu5jg==" => "15023814212",
            "QCjZveKT1I5ZqVzKyK/riA==" => "15074533434",
            "ChG6YsIY+0kHbyhaV3fkLg==" => "15086630348",
            "CAZouUN7HsM0bM8T5OdJxA==" => "15086966768",
            "E8d5v+Oy6Y4ylPTt9ekMSg==" => "15095802280",
            "uzr50/ehY1Vh8Pc0Fu5ssQ==" => "15102306430",
            "WLiM406Vp5LKKZX63SYpCw==" => "15106282487",
            "4t9Pe6kIfIXwvWwqIguTdQ==" => "15178941009",
            "N4fSm+gcJsOCqbVL20XeRQ==" => "15190993598",
            "pBkdvhV9ZKC5DEcuv4MkKA==" => "15213237035",
            "kiwsMdwiVHlcGYW0olpruw==" => "15215100760",
            "FQqk4FSBSrDwiyIhsEmzLQ==" => "15223636412",
            "Vs8gupIHdpYb1ertOd/NDA==" => "15254454888",
            "vbbdykH9L0elNB5IGA5u8Q==" => "15262871892",
            "6Q3G8OmB1wnnoq7a3yyWwA==" => "15262874400",
            "UzBLM/JKZQUxeB3cLa1NUg==" => "15334570160",
            "DKTos4fM3z7gTU+vmK30sA==" => "15523313309",
            "lvxN1KfjoCl1a+xAGeylJA==" => "15523590049",
            "6AEDy4aaqm56hGhIWQZDjg==" => "15608349770",
            "illHGrd7E156H8H0kg+yjw==" => "15685589448",
            "iEtulS4vkcIDX5raWV31fw==" => "15696050057",
            "jQlLemebREreVqhGsNIfzg==" => "15723365097",
            "viQLwvQrar7GILg0CXuXYw==" => "15730080659",
            "bWs9RD94ULWjEDjjNdPG6A==" => "15730299665",
            "W7oyPpC5NMJm9t56L99XfQ==" => "15736175219",
            "y6Qy1QEV+d464fjHLOJHQw==" => "15736507379",
            "n2XVZw045ng4l6xEUTfaqA==" => "15802300806",
            "mej4qubb/jwSDuOUREHdKg==" => "15802366232",
            "I8JSSr8VCclXTvjlxys/bg==" => "15803083307",
            "uS3IUaxEn0uwpsV8AX8gcQ==" => "15803636198",
            "6YRd4Ihvy4ATJucp828jTQ==" => "15823574290",
            "xb+l7h5isUXKwfb2P46b+g==" => "15823742009",
            "Wul5q7T7CD9VvaEDt/Dc+Q==" => "15826166641",
            "a3cemduGol3QCEQ5S2J/QQ==" => "15826493117",
            "k984+W2Xs2gLi1k2dvMmcA==" => "15850507454",
            "EGnVh0Jx4JKQj7n/MYJaug==" => "15862243259",
            "4y+7D6KeQUX5pDVZNFb0XQ==" => "15870479895",
            "qR1jeR1BsDgO1o81j1mung==" => "15883717361",
            "e59qE22Z2cemhFfLyqtCkA==" => "15902306069",
            "Hsgf0TtbJcgePTmeMvNnGQ==" => "15906289870",
            "vf+kHltvSKEu0k0Z1FUsKg==" => "15910821384",
            "x8YUeUUmqsn/CuK5sCPGhg==" => "15922995135",
            "rgSg11JHIFC3t2zZ20uU8g==" => "15923265403",
            "J82cNXKzWBnIBnI+uPQerg==" => "15923484576",
            "ZAyg2PzDdzLzieg92C+kuw==" => "15923808931",
            "Pw1jhqxMcmRE8fbncV4jmQ==" => "15937151506",
            "USud47KZKX0uMJ0UFqexxw==" => "15962857865",
            "siFkFO+iE2CAkkYIiDhoxw==" => "15978947733",
            "0Q0eaqSvjKXTnU/MBTTkkQ==" => "15996536907",
            "XhSiiVgq7qdm7thixWQvRg==" => "16621396254",
            "3DR0c/85r8JCuvw/wqVkRg==" => "16671080304",
            "yNLgn4t0RltkCQRPiAC7+g==" => "17316746714",
            "eOTl0UpQHJ3Os5W5OoXiKA==" => "17388224100",
            "tCbcrM65cz08Ce7nxVoH0Q==" => "17621259509",
            "k/CwNfy38Mci97JkBw5rOA==" => "17621785991",
            "T/mV3otNxhh3rorgkVDPaA==" => "17623230157",
            "D1jcXoZALadEMbo/gmCtSg==" => "17623309874",
            "yAefNvBLP5nbd+JYsBPxLA==" => "17623386589",
            "9CnmgKVBvR1uOfRyDRBigA==" => "17708342011",
            "7BxSo6cjL2tHSQ0WgEzefg==" => "17717353925",
            "riKispvZ7Ml9ZGT0c5GNag==" => "17723550703",
            "ozhWYfHBrQ/Dx4y07/RlBg==" => "17723565195",
            "F4ybshBefkzotSgieQv+Hg==" => "17772336502",
            "lNrkiQDkyS3iOZ7WvEYVvw==" => "17779851842",
            "7nWDsEqlgRxCyribHMiPqg==" => "17783054392",
            "Mk4nvHG7S8Zdf3CtgV46xQ==" => "17830417229",
            "DQ9keLxXEjw/bHb/GlAhbQ==" => "18071625088",
            "lO0s4csz1chhO0NwMTtuxw==" => "18083018881",
            "VT2KaJbHHANHHvFOnbGOwA==" => "18202327581",
            "7CaMK59oV05wQ4grw43t6w==" => "18223115482",
            "WeSfj5jPDoFAICKXEu+sEA==" => "18225277218",
            "v0Xa06lBaRxeF+CmxEA7BA==" => "18225336476",
            "7T3Sc2yGXtf836ZjbiZqxQ==" => "18251393068",
            "7+4UBSBOEIvok/+m9VVPLQ==" => "18290334649",
            "GDSwQcRvLOtYr1WGy/+wcA==" => "18306286690",
            "PzCKsuEkxVijByiwJumSbQ==" => "18323595933",
            "G38sCGgnbNzLzy8B05h9zg==" => "18340815715",
            "FSUq/ZiR1DOuMNKDfjZBnw==" => "18375765326",
            "hLMYay+xsVyBZxIcMpFwHg==" => "18381659405",
            "z889n6UNdqcrkvf4ufXILQ==" => "18383510361",
            "dnTiesbCMR9AcIieUz19Pg==" => "18388291353",
            "Gu4wbFBfioqjrS/kon1Ukw==" => "18398411924",
            "NV+vOXTz3N7VohIwqcmFww==" => "18483606445",
            "MgT7fQH9wQgy3yXGIs5bPg==" => "18516111493",
            "sYfvvFnIUBFUYwPI9Cz+hA==" => "18523102063",
            "yHkJEaOkcMJz9/JsECB/dw==" => "18523508051",
            "BN6WRW1/LIbkfYa4glqL7g==" => "18523520630",
            "X48RtgEk7iNKikafZqO31Q==" => "18523533997",
            "NgFa0vFo/NioXpx4h3O6hg==" => "18523592219",
            "dgBB9YNkLQa3U/BUWaL53Q==" => "18523977718",
            "oo9wSlT5xKxLt5jHHUEQqQ==" => "18584581154",
            "1/xky3C59ggEjCJlcvtHwg==" => "18608014289",
            "hsSU5pQDZ6uMzjOj3GIvEQ==" => "18612972717",
            "naetDpAcE4dXozOjaT2Nbg==" => "18618203501",
            "sIT+L08PcXkQOYEB+dEpIQ==" => "18621792865",
            "8puXV57KyaNtPkElxkQHpw==" => "18623311210",
            "u3EYdJ3VKPETcktCmn/oNg==" => "18623360087",
            "WeAKt6kFKrJEgB7YY1Lkrw==" => "18623372439",
            "yCjKaropb39oBy+MYnQA4Q==" => "18680857113",
            "3BBuNbSI2CpLUJYUqh7ozA==" => "18696533604",
            "oQXhsnpT5LJWHv5uIWfzew==" => "18696616422",
            "itwOlGNPFmfnyk0H0hg6tw==" => "18782243980",
            "UjN03AGZ7QAfwOhxGGC2sw==" => "18812773549",
            "j0fHIYKToIBPzbu/h+AcDQ==" => "18818277246",
            "1HBWT4KUFxKIU4Hf1EGE2Q==" => "18845585970",
            "nqulr2mD/R7PmarDUx/h/A==" => "18848438008",
            "MXdsgjpzn47xrfY9rkx3zg==" => "18883169507",
            "rT7/pFICZ97WdVq6PV5D6g==" => "18883214189",
            "bchbbyy/qq/3i9ysL+F5Eg==" => "18883545451",
            "jVzJZXE3JsPEmDRTdXW+dA==" => "18921635009",
            "mJU2UCOYaC33FEnv13R2ew==" => "18996403640",
            "p7R/3kBQdfknY6dk8GDPTg==" => "19112190678",
            "L3lzv3TQeUHlJwwtOv/SwA==" => "19123207676",
            "Eq6yXYmTh/2kUgUM6x5AlA==" => "19805355588",
            "yQB3k9FNtzWkPvyBjbsnMA==" => "19912492305",
            "FDS8mhlX0weTeQhTKg0KNw==" => "19923226562",
            "9SjRbwM1kZlCHfPQbo9GYQ==" => "19923259675",
            "7wIM+oBik4GDX8ovL+518g==" => "19942331994",
        ];

        $arr3 = [];
        foreach ($arr1 as $uid => $jm_phone){
            $phone = $arr2[$jm_phone] ?? '';
            $arr3[$phone] = $uid;
        }


        $header = ['手机号','券ID', "uid"];

        $user_data = [
            ['13608345999', '6894',],
            ['18523533997', '6894',],
            ['15823574290', '6894',],
            ['18388291353', '6894',],
            ['13426130308', '6894',],
            ['15910821384', '6894',],
            ['15902306069', '6894',],
            ['18883545451', '6894',],
            ['15803083307', '6894',],
            ['13847663799', '6894',],
            ['13482429017', '6894',],
            ['13527525741', '6894',],
            ['18516111493', '6894',],
            ['13637965600', '6894',],
            ['13983664017', '6894',],
            ['13896002202', '6894',],
            ['18782243980', '6894',],
            ['18608014289', '6894',],
            ['17621259509', '6894',],
            ['13482216046', '6894',],
            ['18812773549', '6894',],
            ['17621785991', '6894',],
            ['13533688073', '6894',],
            ['13671678431', '6894',],
            ['19923226562', '6894',],
            ['18202327581', '6894',],
            ['15223636412', '6894',],
            ['15803636198', '6894',],
            ['13650590404', '6894',],
            ['18381659405', '6894',],
            ['13508361427', '6894',],
            ['17623230157', '6894',],
            ['13114065934', '6894',],
            ['13608330352', '6894',],
            ['18323595933', '6894',],
            ['13452140083', '6894',],
            ['18618203501', '6894',],
            ['18883169507', '6894',],
            ['13594763089', '6894',],
            ['17723565195', '6894',],
            ['18996403640', '6894',],
            ['18483606445', '6894',],
            ['13628218772', '6894',],
            ['15213237035', '6894',],
            ['15802300806', '6894',],
            ['15023814212', '6894',],
            ['15870479895', '6894',],
            ['15802366232', '6894',],
            ['15086630348', '6894',],
            ['18523508051', '6894',],
            ['17717353925', '6894',],
            ['17779851842', '6894',],
            ['15523313309', '6894',],
            ['15608349770', '6894',],
            ['13524939088', '6894',],
            ['18071625088', '6894',],
            ['18306286690', '6894',],
            ['13912423290', '6894',],
            ['15106282487', '6894',],
            ['15962857865', '6894',],
            ['15190993598', '6894',],
            ['15906289870', '6894',],
            ['15262871892', '6894',],
            ['13052888102', '6894',],
            ['15262874400', '6894',],
            ['18251393068', '6894',],
            ['18921635009', '6894',],
            ['13862800870', '6894',],
            ['13813631416', '6894',],
            ['18071625088', '6894',],
            ['18071625088', '6894',],
            ['18623311210', '6894',],
            ['17708342011', '6894',],
            ['18523520630', '6894',],
            ['15922995135', '6894',],
            ['15736175219', '6894',],
            ['15923484576', '6894',],
            ['17623386589', '6894',],
            ['15823742009', '6894',],
            ['15923808931', '6894',],
            ['15723365097', '6894',],
            ['18845585970', '6894',],
            ['17316746714', '6894',],
            ['15102306430', '6894',],
            ['18623372439', '6894',],
            ['18883214189', '6894',],
            ['17623309874', '6894',],
            ['19923259675', '6894',],
            ['19123207676', '6894',],
            ['15923265403', '6894',],
            ['17772336502', '6894',],
            ['13617614047', '6894',],
            ['13983888450', '6894',],
            ['18848438008', '6894',],
            ['15696050057', '6894',],
            ['15254454888', '6894',],
            ['15019431962', '6894',],
            ['15937151506', '6894',],
            ['13349862009', '6894',],
            ['18612972717', '6894',],
            ['18818277246', '6894',],
            ['18225336476', '6894',],
            ['18623360087', '6894',],
            ['16621396254', '6894',],
            ['18696533604', '6894',],
            ['18523977718', '6894',],
            ['15215100760', '6894',],
            ['13896724521', '6894',],
            ['18523102063', '6894',],
            ['15023787902', '6894',],
            ['19942331994', '6894',],
            ['13901731030', '6894',],
            ['19805355588', '6894',],
            ['13983010313', '6894',],
            ['13801966252', '6894',],
            ['15862243259', '6894',],
            ['13055528237', '6894',],
            ['18083018881', '6894',],
            ['15978947733', '6894',],
            ['17783054392', '6894',],
            ['15826166641', '6894',],
            ['13983021166', '6894',],
            ['13167960434', '6894',],
            ['17830417229', '6894',],
            ['13983079595', '6894',],
            ['15334570160', '6894',],
            ['16671080304', '6894',],
            ['15730299665', '6894',],
            ['13527317787', '6894',],
            ['18375765326', '6894',],
            ['17388224100', '6894',],
            ['15021159859', '6894',],
            ['18223115482', '6894',],
            ['18290334649', '6894',],
            ['15095802280', '6894',],
            ['15850507454', '6894',],
            ['17723550703', '6894',],
            ['18696616422', '6894',],
            ['18621792865', '6894',],
            ['18680857113', '6894',],
            ['18398411924', '6894',],
            ['13983028475', '6894',],
            ['19912492305', '6894',],
            ['15178941009', '6894',],
            ['13983241864', '6894',],
            ['15685589448', '6894',],
            ['18584581154', '6894',],
            ['13308059058', '6894',],
            ['15730080659', '6894',],
            ['18383510361', '6894',],
            ['18225277218', '6894',],
            ['19112190678', '6894',],
            ['15736507379', '6894',],
            ['15996536907', '6894',],
            ['13677647465', '6894',],
            ['15074533434', '6894',],
            ['15883717361', '6894',],
            ['13962852161', '6894',],
            ['18523592219', '6894',],
            ['13883847221', '6894',],
            ['15086966768', '6894',],
            ['15523590049', '6894',],
            ['15826493117', '6894',],
            ['18340815715', '6894',],
        ];
        foreach ($user_data as &$datum){
            $datum[] = $arr3[$datum[0]] ?? '';
        }

        print_r($this->exportExcel([
            'filename'=> '批量发放优惠券.xls',
            'header'=> $header,
            'data'=> $user_data,
        ]));


        print_r($user_data);
        die;

    }

    public function exportRdCode($param)
    {
        $rd_code = "PO-2023-05-19-0017";
        $env = 'prod'; //dev (测试环境)  or  prod (正式环境)

        $go_db = 1;//云酒
        $to_db = [2, 3, 4]; //科技 松鸽 木兰朵
        $db    = [
            '1' => "{$env}_mysql_wms_stock_1", //云酒
            '2' => "{$env}_mysql_wms_stock_2", //科技
            '3' => "{$env}_mysql_wms_stock_3", //松鸽
            '4' => "{$env}_mysql_wms_stock_4", //木兰朵
        ]; //数据库名 云酒 科技 松鸽 木兰朵

        $storage = Db::connect($db[$go_db])->name('storage')->where('rd_code', $rd_code)->find();

        $storage_in_task = Db::connect($db[$go_db])->name('storage_in_task')
            ->where('storage_id',$storage['storage_id'])->where('status', 3)->select()->toArray(); //清点任务

        $storage_up_task = Db::connect($db[$go_db])->name('storage_up_task')
            ->where('in_task_id', 'in', array_column($storage_in_task, 'task_id'))
            ->where('status', 'in', [1,2,3])
            ->select()->toArray();

        //region 入库单
        $storage_goods = Db::connect($db[$go_db])->name('storage_goods')
            ->where('storage_id', $storage['storage_id']) //调拨入库单ID
            ->select()->toArray();

        $storage_up_task_goods = Db::connect($db[$go_db])->name('storage_up_task_goods')
            ->where('storage_goods_id', 'in', array_column($storage_goods, 'storage_goods_id'))
            ->where('task_id', 'in', array_column($storage_up_task, 'task_id'))
            ->select()->toArray();

        $storage_up_task_goods_group = [];
        foreach ($storage_up_task_goods as $sub_var1) {
            $storage_up_task_goods_group[$sub_var1['storage_goods_id']][] = $sub_var1;
        }

        #清点
        $storage_in_task_goods = Db::connect($db[$go_db])->name('storage_in_task_goods')
            ->where('storage_goods_id', 'in', array_column($storage_goods, 'storage_goods_id'))
            ->where('task_id', 'in', array_column($storage_in_task,'task_id'))
            ->select()->toArray();

        $storage_in_task_goods_group = [];
        foreach ($storage_in_task_goods as $sub_var2) {
            $storage_in_task_goods_group[$sub_var2['storage_goods_id']][] = $sub_var2;
        }

        $storage_goods_group = [];
        foreach ($storage_goods as $storage_goods_item) {
            $up_task_list                       = $storage_up_task_goods_group[$storage_goods_item['storage_goods_id']] ?? [];
            $storage_goods_item['up_task_list'] = $up_task_list;

            $in_task_list                       = $storage_in_task_goods_group[$storage_goods_item['storage_goods_id']] ?? [];
            $storage_goods_item['in_task_list'] = $in_task_list;

            $storage_goods_group[$storage_goods_item['storage_id']][] = $storage_goods_item;
        }

        $goods_list = $storage_goods_group[$storage['storage_id']];

        $data = [];
        foreach ($goods_list as $goods_info){

            $data[] = [
                $storage['rd_code'],
                $goods_info['bar_code'],
                array_sum(array_column($goods_info['in_task_list'],'in_number')), //清点-清点数量
                array_sum(array_column($goods_info['in_task_list'],'lack_number')), //清点-缺货数量
                array_sum(array_column($goods_info['in_task_list'],'back_number')), //清点-退回数量
                array_sum(array_column($goods_info['in_task_list'],'reissue_number')), //清点-补发数量
                array_sum(array_column($goods_info['up_task_list'],'good_number')), //上架-良品数量
                array_sum(array_column($goods_info['up_task_list'],'bad_number')), //上架-次品数量
            ];

        }

        $header = [
            '调拨单',
            '条码',
            '清点-清点数量',
            '清点-缺货数量',
            '清点-退回数量',
            '清点-补发数量',
            '上架-良品数量',
            '上架-次品数量',
        ];


        print_r($this->exportExcel([
            'filename'=> "PO单{$rd_code}.xls",
            'header'=> $header,
            'data'=> $data,
        ]));die;


        print_r($goods_list);die ;


        //endregion 入库单

        return $this->success($storage);
    }


}



