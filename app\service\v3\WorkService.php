<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Work;
use app\service\es\Es;
use app\service\Order as OrderService;
use app\validate\WorkValidate;
use think\Exception;
use think\facade\Db;

/**
 * 工单
 * Class WorkService
 * @package app\service\v3
 */
class WorkService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Work;
        $this->validate    = WorkValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/06/26 15:22
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }


    public function waybillsKuaidi100($param)
    {
        $data = Db::connect('mysql_customer_service_prod')->name('waybills')
            ->where('waybill', 'like', "%JD%")
            ->where('status', 0)
            ->column('id,waybill,expressType');

        $waybill_ids = [];
        foreach ($data as $datum) {
            $waybill_ids[$datum['waybill']]['ids'][]       = $datum['id'];
            $waybill_ids[$datum['waybill']]['expressType'] = $datum['expressType'];
        }

        $done_ids = [];

        foreach ($waybill_ids as $waybill => $item) {
            $waybill_info = \Curl::kuaidi100MapTrack([
                'logisticCode' => $waybill,
                'expressType'  => $item['expressType'],
            ]);
            if (!empty($waybill_info)) {

                if ($waybill_info['state'] == 3) {
                    $done_ids = array_merge($done_ids, $item['ids']);
                }
            }
        }

        $nums = Db::connect('mysql_customer_service_prod')->name('waybills')
            ->where('id', 'in', $done_ids)
            ->update(['status' => 1]);


        return json_encode([$nums,
            count($done_ids),
            $done_ids,]);


    }

    public function bftkdd($param)
    {
        $desc = '订单部分退款';
//        $unit_amount = 10;
//        $db          = 'database_orders_prod';

        die ($desc);

        $db          = 'database_orders_dev'; // todo
        $unit_amount = 0.01;


//        $periods = Es::name(Es::PERIODS)
//            ->where([
//                ['_id', 'in', [172186, 172085, 172078]]
//            ])->select()->toArray();

        $orders = Db::connect($db)->name('flash_order')->alias('o')
            ->join("order_main m", 'o.main_order_id=m.id')
            ->where([
                ['o.sub_order_status', 'in', [1, 2, 3]],
                ['period', 'in', [172186, 172085, 172078]],
//                ['refund_status', 'in', [0, 1, 3]],
            ])->column('m.payment_subject,m.payment_method,m.main_order_no,o.period,o.uid,o.order_type,o.sub_order_no,o.sub_order_status,o.cash_amount,o.payment_amount,o.refund_money,o.refund_status,o.order_qty');


        $out[$desc][] = ['期数', '订单号', '支付金额', '套餐份数', '退款金额', '退款结果'];

        foreach ($orders as $order) {
            Db::connect($db)->startTrans();
            try {
                $need_refund_money = bcmul($order['order_qty'], $unit_amount, 2);

                if (in_array($order['refund_status'], [1, 2])) {
                    throw new Exception('订单退款状态: ' . \Config::ORDER_REFUND_STATUS[$order['refund_status']]);
                }
                Db::connect($db)->name('flash_order')
                    ->where(['sub_order_no' => $order['sub_order_no']])
                    ->update([
                        'remarks'       => "172186  172085  172078 退款10元每份",
                        'refund_status' => 2,
                        'refund_money'  => bcadd($need_refund_money, $order['refund_money'], 2)
                    ]);
                Db::connect($db)->name('order_remarks')->insert([
                    'sub_order_no' => $order['sub_order_no'],
                    'admin_id'     => 0,
                    'remarks'      => "172186  172085  172078 退款10元每份",
                    'created_time' => time()
                ]);
                $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $order['uid']);
                //工单记录
                $main_id = Db::connect($db)->table('vh_customer_service.vh_work_order')->insertGetId([
                    'uid'                   => $order['uid'],
                    'work_order_no'         => generateTaskNo(),
                    'order_no'              => $order['sub_order_no'],
                    'period'                => $order['period'],
                    'pay_actual'            => bcadd($order['payment_amount'], $order['refund_money'], 2),
                    'payment_method'        => $order['payment_method'],
                    'order_type'            => 0,
                    'sub_order_status'      => 4,
                    'gd_do_status'          => 8,
                    'gd_status'             => 4,
                    'end_type'              => 0,
                    'work_order_type'       => 11,
                    'after_sales_reason'    => "172186  172085  172078 退款10元每份",
                    'createdor'             => "系统",
                    'created_id'            => 0,
                    'work_order_manage'     => "系统",
                    'work_order_manage_uid' => 0,
                    'refund_no'             => $refund_order_no,
                    'created_time'          => time(),
                    'handle_time'           => time(),
                    'handle_end_time'       => time(),
                    'end_time'              => time(),
                    'is_fast_refund'        => 1,
                ]);
                $sub_id  = Db::connect($db)->table('vh_customer_service.vh_refunds_price_differences')->insertGetId([
                    'gd_id'            => $main_id,
                    'return_reason'    => '172186  172085  172078 退款10元每份',
                    'return_money'     => $need_refund_money,
                    'refund_balance'   => 0,
                    'period'           => $order['period'],
                    'main_order_no'    => $order['main_order_no'],
                    'customer_name'    => '',
                    'is_return_coupon' => 0,
                    'coupon_id'        => 0,
                ]);
                //退款信息组装
                if (in_array($order['payment_subject'], [1, 2])) {
                    //银联退款
                    $pushData    = array(
                        'main_order_no'   => $order['payment_method'] . $order['main_order_no'],//支付方式+主订单号为银联交易订单号
                        'payment_method'  => $order['payment_method'],
                        'refund_amount'   => $need_refund_money,
                        'refund_order_no' => $refund_order_no,
                        'subject'         => $order['payment_subject'],
                        'is_cross'        => $order['order_type'] == 2 ? 1 : 0,
                        'order_type'      => 0,
                        'sub_order_no'    => $order['sub_order_no'],
                    );
                    $code        = httpCurl(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', 'post', json_encode($pushData), 30);
                    $orderRefund = json_decode($code, true);
                    if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                        throw new \Exception('发起退款失败：' . $orderRefund['error_msg']);
                    }
                } else {
                    throw new Exception('未处理的 payment_subject ' . $order['payment_subject']);
                }
                $out[$desc][] = [$order['period'], $order['sub_order_no'], $order['payment_amount'], $order['order_qty'], $need_refund_money, $code];

                print_r($order['sub_order_no'] . ' SUCCESS' . PHP_EOL);
                Db::connect($db)->commit();
            } catch (\Exception $e) {
                Db::connect($db)->rollback();
                $out[$desc][] = [$order['period'], $order['sub_order_no'], $order['payment_amount'], $order['order_qty'], 0, $e->getMessage()];
                print_r($order['sub_order_no'] . ' ERROR' . $e->getMessage() . PHP_EOL);
            }
        }
        print_r(outExcel($out, $desc . time()));
    }


    public function bjshtopuser($param)
    {
        $desc = "北京上海TOP100消费用户";
        $db   = 'database_orders_prod';

        $citys = ['2' => '北京', '10' => '上海'];

        $total = [];
        $uids  = [];
        $out   = [];

        try {
            foreach ($citys as $city_id => $city_name) {
                foreach (range(2015, 2025) as $y) {
                    foreach (range(1, 12) as $m) {
                        $stime = date('Y-m-d H:i:s', strtotime("{$y}-{$m}-01 00:00:00"));
                        $etime = date('Y-m-d H:i:s', strtotime($stime . '+1month -1second'));
                        $where = [
                            ['sub_order_status', 'in', [1, 2, 3]],
                            ['payment_time', '>=', $stime],
                            ['payment_time', '<=', $etime],
//                            ['refund_status', 'in', [0, 1, 3]],
                            ['order_type', 'in', [0, 1, 2, 3, 9]],
                            ['province_id', '==', $city_id],
                        ];


                        #region 闪购 秒发 跨境 尾货
                        $orders = Es::name(Es::ORDERS)->where($where)
                            ->limit(0, 200000)
                            ->order(['id' => "ASC"])
                            ->field('uid,payment_amount,refund_money')
                            ->select()->toArray();

                        foreach ($orders as $order) {
                            $uids[$order['uid']] = $order['uid'];
                            $payment             = bcsub($order['payment_amount'], $order['refund_money'], 2);
                            if ($payment > 0) {
                                $total[$city_name]['total'][$order['uid']]['uid']     = $order['uid'];
                                $total[$city_name]['total'][$order['uid']]['num']     = bcadd(($total[$city_name]['total'][$order['uid']]['num'] ?? 0), 1);
                                $total[$city_name]['total'][$order['uid']]['payment'] = bcadd(($total[$city_name]['total'][$order['uid']]['payment'] ?? 0), $payment, 2);

                                if ($y == 2025) {
                                    $total[$city_name][$y][$order['uid']]['uid']     = $order['uid'];
                                    $total[$city_name][$y][$order['uid']]['num']     = bcadd(($total[$city_name][$y][$order['uid']]['num'] ?? 0), 1);
                                    $total[$city_name][$y][$order['uid']]['payment'] = bcadd(($total[$city_name][$y][$order['uid']]['payment'] ?? 0), $payment, 2);
                                }
                            }
                        }

                        print_r("{$city_name} {$y}-{$m}" . PHP_EOL);
                    }
                }
                $out[$city_name . '总计'][] = ['TOP', '用户ID', '用户昵称', '注册时间', '订单数量', '消费金额'];
                $out[$city_name . '2025'][] = ['TOP', '用户ID', '用户昵称', '注册时间', '订单数量', '消费金额'];
            }

            $uids = Db::connect($db)->table('vh_user.vh_user')->where('uid', 'in', array_keys($uids))
                ->column('uid,nickname,created_time', 'uid');


            foreach ($total as $ctname => $ctinfo) {
                $ct_total   = $ctinfo['total'];
                $ct_total25 = $ctinfo['2025'];

                uasort($ct_total, function ($a, $b) {
                    return $b['payment'] <=> $a['payment'];
                });
                $ct_total = array_slice($ct_total, 0, 100, true);

                $i = 1;
                foreach ($ct_total as $ctvalue) {
                    $out[$ctname . '总计'][] = [$i,
                        $ctvalue['uid'],
                        $uids[$ctvalue['uid']]['nickname'] ?? '',
                        ($uids[$ctvalue['uid']]['created_time'] ?? 0) ? date('Y-m-d H:i:s', $uids[$ctvalue['uid']]['created_time']) : '',
                        $ctvalue['num'],
                        $ctvalue['payment']
                    ];
                    $i++;
                }


                uasort($ct_total25, function ($a, $b) {
                    return $b['payment'] <=> $a['payment'];
                });
                $ct_total25 = array_slice($ct_total25, 0, 100, true);

                $i = 1;
                foreach ($ct_total25 as $ctvalue) {
                    $out[$ctname . '2025'][] = [
                        $i,
                        intval($ctvalue['uid']),
                        $uids[$ctvalue['uid']]['nickname'] ?? '',
                        ($uids[$ctvalue['uid']]['created_time'] ?? 0) ? date('Y-m-d H:i:s', $uids[$ctvalue['uid']]['created_time']) : '',
                        intval($ctvalue['num']),
                        floatval($ctvalue['payment'])
                    ];
                    $i++;
                }
            }

            print_r(outExcel($out, $desc));
            die;

        } catch (\Exception $e) {
            $msg = $e->getMessage() . $e->getLine();
            print_r($msg);
            die;
        }
    }

    public function changeFieldType($param)
    {
        $desc = "修改excel字段格式";

        $in_data = readExcel('tpp100.xlsx');


//        print_r($in_data);die ;

        $out = [];
        $uids = [];

        foreach ($in_data['sheet'] as $k => $v) {
            $list = $v['list'];
            foreach ($list as $j =>  &$item){
                if($j != 0){
                    $item[0] = intval($item[0]);
                    $item[1] = intval($item[1]);
                    $item[2] = strval($item[2]);
                    $item[3] = strval($item[3]);
                    $item[4] = intval($item[4]);
                    $item[5] = floatval($item[5]);
                }
            }
            $out[$v['name']] = $list;
            unset($list);
        }

//        $bj_uids = array_values(array_unique(array_merge($uids[1],$uids[2])));
//        $sh_uids = array_values(array_unique(array_merge($uids[3],$uids[4])));
//
//
//        print_r(array_intersect($bj_uids,$sh_uids));die ;


            print_r(outExcel($out, $desc));
            die;

    }

}



