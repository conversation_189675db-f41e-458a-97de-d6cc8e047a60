<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ArticlesValidate
 * @package app\validate
 */
class ArticlesValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'wxid|wxid' => 'require|number',  //wxid
        'content|content' => 'max:65535',  //content

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'content',],
        'edit'   => [ 'wxid', 'content',],
        'detail' => [ 'wxid',],
        'del'    => [ 'wxid',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'wxid', 'content', ])->remove('wxid', 'require')
            ;
    }




}