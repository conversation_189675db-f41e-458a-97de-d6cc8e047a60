<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Store;
use app\validate\StoreValidate;
use think\facade\Db;

/**
 * 线下门店
 * Class StoreService
 * @package app\service\v3
 */
class StoreService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Store;
        $this->validate    = StoreValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/08/29 17:15
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }

    public function aliConfigAdd($param)
    {
        $key         = 'E32188081F709D4AC9357469AE184F18';
        $cipher_algo = 'AES-128-CBC';
        $ivlen       = openssl_cipher_iv_length($cipher_algo);
        $iv          = openssl_random_pseudo_bytes($ivlen);


        //加密
        $param['appId']              = base64_encode(openssl_encrypt($param['appId'], $cipher_algo, $key, OPENSSL_RAW_DATA, $iv));
        $param['merchantPrivateKey'] = base64_encode(openssl_encrypt($param['merchantPrivateKey'], $cipher_algo, $key, OPENSSL_RAW_DATA, $iv));
        $param['alipayPublicKey']    = base64_encode(openssl_encrypt($param['merchantPrivateKey'], $cipher_algo, $key, OPENSSL_RAW_DATA, $iv));

        $res = Db::connect('dev_mysql_osms_store')->name('aliconfig')->insert($param);

        var_dump($res);
        die;
    }

}



